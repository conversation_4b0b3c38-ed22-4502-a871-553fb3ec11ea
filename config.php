<?php
// Database configuration
$db_host = 'localhost';
$db_name = 'brendh5_checkup';
$db_user = 'brendh5_checkup_user';
$db_pass = 'Zefer*2021';

// Application settings
$app_name = 'Audit System';
$app_url = 'https://checkup.nurcollection.az';
$upload_dir = 'uploads/';

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Connect to database
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);

    // Set MySQL timezone to match PHP timezone
    $pdo->exec("SET time_zone = '+04:00'");
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Define constants
define('BASE_PATH', __DIR__);
define('UPLOAD_PATH', BASE_PATH . '/' . $upload_dir);

// Set timezone
date_default_timezone_set('Asia/Baku');

// Include image functions
require_once 'image_functions.php';
