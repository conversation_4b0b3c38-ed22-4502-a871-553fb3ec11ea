<?php
require_once 'config.php';
require_once 'functions.php';

/**
 * Login user
 * 
 * @param string $username Username
 * @param string $password Password
 * @return boolean True if login successful, false otherwise
 */
function login($username, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT u.*, r.name as role FROM users u 
                          JOIN roles r ON u.role_id = r.id 
                          WHERE u.username = :username");
    $stmt->execute(['username' => $username]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        // Password is correct, create session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_username'] = $user['username'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_role_id'] = $user['role_id'];
        
        return true;
    }
    
    return false;
}

/**
 * Register new user
 * 
 * @param string $username Username
 * @param string $password Password
 * @param int $role_id Role ID
 * @return boolean True if registration successful, false otherwise
 */
function register($username, $password, $role_id) {
    global $pdo;
    
    try {
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert user
        $stmt = $pdo->prepare("INSERT INTO users (username, password, role_id) VALUES (:username, :password, :role_id)");
        $stmt->execute([
            'username' => $username,
            'password' => $hashed_password,
            'role_id' => $role_id
        ]);
        
        return true;
    } catch (PDOException $e) {
        // Username already exists or other error
        return false;
    }
}

/**
 * Update user
 * 
 * @param int $id User ID
 * @param string $username Username
 * @param string $password Password (optional)
 * @param int $role_id Role ID
 * @return boolean True if update successful, false otherwise
 */
function updateUser($id, $username, $password, $role_id) {
    global $pdo;
    
    try {
        if (!empty($password)) {
            // Update with new password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET username = :username, password = :password, role_id = :role_id WHERE id = :id");
            $stmt->execute([
                'username' => $username,
                'password' => $hashed_password,
                'role_id' => $role_id,
                'id' => $id
            ]);
        } else {
            // Update without changing password
            $stmt = $pdo->prepare("UPDATE users SET username = :username, role_id = :role_id WHERE id = :id");
            $stmt->execute([
                'username' => $username,
                'role_id' => $role_id,
                'id' => $id
            ]);
        }
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Delete user
 * 
 * @param int $id User ID
 * @return boolean True if deletion successful, false otherwise
 */
function deleteUser($id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("DELETE FROM users WHERE id = :id");
        $stmt->execute(['id' => $id]);
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Get all users
 * 
 * @return array Array of users
 */
function getAllUsers() {
    global $pdo;
    
    $stmt = $pdo->query("SELECT u.*, r.name as role FROM users u JOIN roles r ON u.role_id = r.id ORDER BY u.username");
    return $stmt->fetchAll();
}

/**
 * Get all roles
 * 
 * @return array Array of roles
 */
function getAllRoles() {
    global $pdo;
    
    $stmt = $pdo->query("SELECT * FROM roles ORDER BY name");
    return $stmt->fetchAll();
}

/**
 * Logout user
 * 
 * @return void
 */
function logout() {
    // Unset all session variables
    $_SESSION = [];
    
    // Destroy the session
    session_destroy();
}

/**
 * Require login to access page
 * 
 * @return void Redirects to login page if not logged in
 */
function requireLogin() {
    if (!isLoggedIn()) {
        flash('login_message', 'Davam etmək üçün daxil olmalısınız', 'alert alert-danger');
        redirect('index.php');
    }

    // Refresh user session data to ensure consistency
    refreshUserSession();
}

/**
 * Require specific permission to access page
 * 
 * @param string $permission Permission required
 * @return void Redirects to dashboard if permission not granted
 */
function requirePermission($permission) {
    requireLogin();
    
    if (!hasPermission($permission)) {
        flash('permission_message', 'Bu səhifəyə giriş icazəniz yoxdur', 'alert alert-danger');
        redirect('dashboard.php');
    }
}
