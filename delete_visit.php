<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login
requireLogin();

// Only <PERSON><PERSON> and <PERSON><PERSON><PERSON> can delete visits
if ($_SESSION['user_role'] !== 'Admin' && $_SESSION['user_role'] !== 'Menecer') {
    flash('error_message', 'Bu əməliyyat üçün icazəniz yoxdur', 'alert alert-danger');
    redirect('visit_plan.php');
}

// Get visit ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış ziyarət ID', 'alert alert-danger');
    redirect('visit_plan.php');
}

// Get visit data
global $pdo;
$stmt = $pdo->prepare("SELECT * FROM visit_plans WHERE id = :id");
$stmt->execute(['id' => $id]);
$visit = $stmt->fetch();

if (!$visit) {
    flash('error_message', 'Ziyarət tapılmadı', 'alert alert-danger');
    redirect('visit_plan.php');
}

// Get month and year for redirect
$month = date('n', strtotime($visit['visit_date']));
$year = date('Y', strtotime($visit['visit_date']));

// Delete visit
$stmt = $pdo->prepare("DELETE FROM visit_plans WHERE id = :id");
$stmt->execute(['id' => $id]);

flash('success_message', 'Ziyarət uğurla silindi');
redirect('visit_plan.php?month=' . $month . '&year=' . $year);
