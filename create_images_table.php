<?php
// Bu skript audit_item_images cədvəlini yaradır

require_once 'config.php';
require_once 'functions.php';

// Düzəltmə haqqında məlumat
echo "<h1><PERSON>əkill<PERSON>r cədvəli yarat<PERSON> skripti</h1>";
echo "<p>Bu skript audit_item_images cədvəlini yaradır.</p>";

try {
    global $pdo;
    
    // Cədvəli yarat
    $sql = "CREATE TABLE IF NOT EXISTS `audit_item_images` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `audit_item_id` int(11) NOT NULL,
      `image` varchar(255) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `audit_item_id` (`audit_item_id`),
      CONSTRAINT `audit_item_images_ibfk_1` FOREIGN KEY (`audit_item_id`) REFERENCES `audit_items` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    $pdo->exec($sql);
    
    // Mövcud şəkilləri yeni cədvələ köçür
    $stmt = $pdo->query("SELECT id, image FROM audit_items WHERE image IS NOT NULL AND image != ''");
    $items = $stmt->fetchAll();
    
    foreach ($items as $item) {
        $stmt = $pdo->prepare("INSERT INTO audit_item_images (audit_item_id, image) VALUES (:audit_item_id, :image)");
        $stmt->execute([
            'audit_item_id' => $item['id'],
            'image' => $item['image']
        ]);
    }
    
    echo "<p style='color: green;'>Şəkillər cədvəli uğurla yaradıldı və mövcud şəkillər köçürüldü!</p>";
    echo "<p><a href='index.php'>Giriş səhifəsinə qayıt</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Xəta baş verdi: " . $e->getMessage() . "</p>";
}
?>
