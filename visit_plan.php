<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('manage_visit_plan');

// Get current month and year
$month = isset($_GET['month']) ? (int)$_GET['month'] : date('n');
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');

// Validate month and year
if ($month < 1 || $month > 12) {
    $month = date('n');
}

if ($year < 2000 || $year > 2100) {
    $year = date('Y');
}

// Get first day of the month
$firstDay = mktime(0, 0, 0, $month, 1, $year);

// Get number of days in the month
$numDays = date('t', $firstDay);

// Get day of week for the first day (0 = Sunday, 6 = Saturday)
$dayOfWeek = date('w', $firstDay);
$dayOfWeek = $dayOfWeek === '0' ? 7 : $dayOfWeek; // Convert Sunday from 0 to 7

// Get month name
$monthName = date('F', $firstDay);

// Get previous and next month
$prevMonth = $month - 1;
$prevYear = $year;
if ($prevMonth < 1) {
    $prevMonth = 12;
    $prevYear--;
}

$nextMonth = $month + 1;
$nextYear = $year;
if ($nextMonth > 12) {
    $nextMonth = 1;
    $nextYear++;
}

// Get stores
$stores = getAllStores();

// Get visit plans for the month
global $pdo;
$stmt = $pdo->prepare("SELECT vp.*, s.name as store_name, u.username as username
                      FROM visit_plans vp
                      JOIN stores s ON vp.store_id = s.id
                      JOIN users u ON vp.user_id = u.id
                      WHERE MONTH(vp.visit_date) = :month AND YEAR(vp.visit_date) = :year");
$stmt->execute([
    'month' => $month,
    'year' => $year
]);
$visitPlans = $stmt->fetchAll();

// Organize visit plans by date
$plansByDate = [];
foreach ($visitPlans as $plan) {
    $day = (int)date('j', strtotime($plan['visit_date']));
    if (!isset($plansByDate[$day])) {
        $plansByDate[$day] = [];
    }
    $plansByDate[$day][] = $plan;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $storeId = (int)$_POST['store_id'];
    $visitDate = $_POST['visit_date'];
    $notes = sanitize($_POST['notes']);

    // Validate input
    if ($storeId <= 0 || empty($visitDate)) {
        flash('error_message', 'Zəhmət olmasa mağaza və tarix seçin', 'alert alert-danger');
    } else {
        try {
            // Insert visit plan
            $stmt = $pdo->prepare("INSERT INTO visit_plans (store_id, user_id, visit_date, notes) VALUES (:store_id, :user_id, :visit_date, :notes)");
            $stmt->execute([
                'store_id' => $storeId,
                'user_id' => $_SESSION['user_id'],
                'visit_date' => $visitDate,
                'notes' => $notes
            ]);

            flash('success_message', 'Ziyarət planı uğurla yaradıldı');
            redirect('visit_plan.php?month=' . $month . '&year=' . $year);
        } catch (PDOException $e) {
            flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
        }
    }
}

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>Ziyarət planı</h1>
        <a href="dashboard.php" class="btn btn-sm btn-secondary mt-2">
            <i class="fas fa-arrow-left"></i> Ana səhifəyə qayıt
        </a>
    </div>

    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addVisitModal">
        <i class="fas fa-plus"></i> Yeni ziyarət
    </button>
</div>

<div class="calendar-container">
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <a href="visit_plan.php?month=<?php echo $prevMonth; ?>&year=<?php echo $prevYear; ?>" class="btn btn-sm btn-light">
                    <i class="fas fa-chevron-left"></i> Əvvəlki ay
                </a>

                <div class="date-selector">
                    <div class="dropdown d-inline-block">
                        <button class="btn btn-light dropdown-toggle" type="button" id="yearDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <?php echo $year; ?>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="yearDropdown">
                            <?php for ($y = date('Y') - 5; $y <= 2050; $y++): ?>
                            <li><a class="dropdown-item <?php echo $y == $year ? 'active' : ''; ?>" href="visit_plan.php?month=<?php echo $month; ?>&year=<?php echo $y; ?>"><?php echo $y; ?></a></li>
                            <?php endfor; ?>
                        </ul>
                    </div>

                    <div class="dropdown d-inline-block ms-2">
                        <button class="btn btn-light dropdown-toggle" type="button" id="monthDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <?php echo $monthName; ?>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="monthDropdown">
                            <?php
                            $months = [
                                1 => 'Yanvar', 2 => 'Fevral', 3 => 'Mart', 4 => 'Aprel',
                                5 => 'May', 6 => 'İyun', 7 => 'İyul', 8 => 'Avqust',
                                9 => 'Sentyabr', 10 => 'Oktyabr', 11 => 'Noyabr', 12 => 'Dekabr'
                            ];
                            foreach ($months as $m => $name):
                            ?>
                            <li><a class="dropdown-item <?php echo $m == $month ? 'active' : ''; ?>" href="visit_plan.php?month=<?php echo $m; ?>&year=<?php echo $year; ?>"><?php echo $name; ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>

                <a href="visit_plan.php?month=<?php echo $nextMonth; ?>&year=<?php echo $nextYear; ?>" class="btn btn-sm btn-light">
                    Növbəti ay <i class="fas fa-chevron-right"></i>
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered m-0">
                    <thead class="table-light">
                        <tr>
                            <th width="14.28%">Bazar ertəsi</th>
                            <th width="14.28%">Çərşənbə axşamı</th>
                            <th width="14.28%">Çərşənbə</th>
                            <th width="14.28%">Cümə axşamı</th>
                            <th width="14.28%">Cümə</th>
                            <th width="14.28%">Şənbə</th>
                            <th width="14.28%">Bazar</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <?php
                            // Add empty cells for days before the first day of the month
                            for ($i = 1; $i < $dayOfWeek; $i++) {
                                echo '<td class="calendar-day bg-light"></td>';
                            }

                            // Add cells for each day of the month
                            for ($day = 1; $day <= $numDays; $day++) {
                                // If we've reached the end of the week, start a new row
                                if ($dayOfWeek > 7) {
                                    echo '</tr><tr>';
                                    $dayOfWeek = 1;
                                }

                                // Highlight current day
                                $isToday = ($day == date('j') && $month == date('n') && $year == date('Y'));
                                $dayClass = $isToday ? 'calendar-day today' : 'calendar-day';

                                echo '<td class="' . $dayClass . '">';
                                echo '<div class="calendar-day-header">' . $day . '</div>';

                                // Display visit plans for this day
                                if (isset($plansByDate[$day])) {
                                    foreach ($plansByDate[$day] as $plan) {
                                        echo '<div class="calendar-event" data-bs-toggle="tooltip" title="' . $plan['notes'] . '">';
                                        echo '<span>' . $plan['store_name'] . ' (' . $plan['username'] . ')</span> ';

                                        // Only Admin and Menecer can delete visits
                                        if ($_SESSION['user_role'] === 'Admin' || $_SESSION['user_role'] === 'Menecer') {
                                            echo '<a href="delete_visit.php?id=' . $plan['id'] . '" class="text-white btn-delete" title="Sil"><i class="fas fa-times"></i></a>';
                                        }

                                        echo '</div>';
                                    }
                                }

                                echo '</td>';

                                $dayOfWeek++;
                            }

                            // Add empty cells for days after the last day of the month
                            while ($dayOfWeek <= 7) {
                                echo '<td class="calendar-day bg-light"></td>';
                                $dayOfWeek++;
                            }
                            ?>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Visit Modal -->
<div class="modal fade" id="addVisitModal" tabindex="-1" aria-labelledby="addVisitModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addVisitModalLabel">Yeni ziyarət planı</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="visit_plan.php?month=<?php echo $month; ?>&year=<?php echo $year; ?>" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="store_id" class="form-label">Mağaza *</label>
                        <select class="form-select" id="store_id" name="store_id" required>
                            <option value="">Mağaza seçin</option>
                            <?php foreach ($stores as $store): ?>
                            <option value="<?php echo $store['id']; ?>">
                                <?php echo $store['name']; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="visit_date" class="form-label">Ziyarət tarixi *</label>
                        <input type="date" class="form-control" id="visit_date" name="visit_date" required>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Qeydlər</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Ləğv et</button>
                    <button type="submit" class="btn btn-primary">Yadda saxla</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'footer.php'; ?>
