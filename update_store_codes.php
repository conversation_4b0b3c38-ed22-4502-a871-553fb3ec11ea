<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and admin role
requireLogin();

if ($_SESSION['user_role'] !== 'Admin') {
    flash('error_message', 'Bu səhifəyə giriş icazəniz yoxdur', 'alert alert-danger');
    redirect('dashboard.php');
}

// Get all stores
global $pdo;
$stmt = $pdo->query("SELECT * FROM stores ORDER BY id");
$stores = $stmt->fetchAll();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Update each store
        foreach ($_POST['store_code'] as $storeId => $storeCode) {
            $stmt = $pdo->prepare("UPDATE stores SET 
                store_code = :store_code,
                name = :name,
                address = :address,
                phone = :phone,
                email = :email,
                manager = :manager,
                notes = :notes
                WHERE id = :id");
                
            $stmt->execute([
                'store_code' => sanitize($storeCode),
                'name' => sanitize($_POST['name'][$storeId]),
                'address' => sanitize($_POST['address'][$storeId]),
                'phone' => sanitize($_POST['phone'][$storeId]),
                'email' => sanitize($_POST['email'][$storeId]),
                'manager' => sanitize($_POST['manager'][$storeId]),
                'notes' => sanitize($_POST['notes'][$storeId]),
                'id' => $storeId
            ]);
        }
        
        // Commit transaction
        $pdo->commit();
        
        flash('success_message', 'Mağaza məlumatları uğurla yeniləndi');
        redirect('stores.php');
    } catch (PDOException $e) {
        // Rollback transaction on error
        $pdo->rollBack();
        flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
    }
}

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>Mağaza məlumatlarını yenilə</h1>
        <a href="stores.php" class="btn btn-sm btn-secondary mt-2">
            <i class="fas fa-arrow-left"></i> Mağazalar siyahısına qayıt
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Mağaza məlumatlarını toplu şəkildə yeniləyin</h5>
    </div>
    <div class="card-body">
        <form action="update_store_codes.php" method="post">
            <div class="table-responsive">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Mağaza kodu</th>
                            <th>Mağaza adı</th>
                            <th>Ünvan</th>
                            <th>Telefon</th>
                            <th>E-mail</th>
                            <th>Mağaza meneceri</th>
                            <th>Qeyd</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($stores as $store): ?>
                        <tr>
                            <td><?php echo $store['id']; ?></td>
                            <td>
                                <input type="text" class="form-control" name="store_code[<?php echo $store['id']; ?>]" value="<?php echo $store['store_code'] ?? ''; ?>">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="name[<?php echo $store['id']; ?>]" value="<?php echo $store['name']; ?>" required>
                            </td>
                            <td>
                                <input type="text" class="form-control" name="address[<?php echo $store['id']; ?>]" value="<?php echo $store['address'] ?? ''; ?>">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="phone[<?php echo $store['id']; ?>]" value="<?php echo $store['phone'] ?? ''; ?>">
                            </td>
                            <td>
                                <input type="email" class="form-control" name="email[<?php echo $store['id']; ?>]" value="<?php echo $store['email'] ?? ''; ?>">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="manager[<?php echo $store['id']; ?>]" value="<?php echo $store['manager'] ?? ''; ?>">
                            </td>
                            <td>
                                <textarea class="form-control" name="notes[<?php echo $store['id']; ?>]" rows="2"><?php echo $store['notes'] ?? ''; ?></textarea>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                <a href="stores.php" class="btn btn-secondary me-md-2">Ləğv et</a>
                <button type="submit" class="btn btn-primary">Yadda saxla</button>
            </div>
        </form>
    </div>
</div>

<?php include 'footer.php'; ?>
