<?php
require_once 'config.php';

/**
 * Sanitize user input
 *
 * @param string $data Input data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Redirect to a specific page
 *
 * @param string $location URL to redirect to
 * @return void
 */
function redirect($location) {
    header("Location: $location");
    exit;
}

/**
 * Display flash message
 *
 * @param string $name Message name
 * @param string $message Message content
 * @param string $class CSS class for styling
 * @return void
 */
function flash($name = '', $message = '', $class = 'alert alert-success') {
    if (!empty($name)) {
        if (!empty($message) && empty($_SESSION[$name])) {
            $_SESSION[$name] = $message;
            $_SESSION[$name . '_class'] = $class;
        } else if (empty($message) && !empty($_SESSION[$name])) {
            $class = !empty($_SESSION[$name . '_class']) ? $_SESSION[$name . '_class'] : $class;
            echo '<div class="' . $class . '" id="msg-flash">' . $_SESSION[$name] . '</div>';
            unset($_SESSION[$name]);
            unset($_SESSION[$name . '_class']);
        }
    }
}

/**
 * Check if user is logged in
 *
 * @return boolean True if logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Check if user has permission
 *
 * @param string $permission Permission to check
 * @return boolean True if has permission, false otherwise
 */
function hasPermission($permission) {
    global $pdo;

    if (!isLoggedIn()) {
        return false;
    }

    // Admin has all permissions
    if ($_SESSION['user_role'] === 'Admin') {
        return true;
    }

    // Get user role permissions
    $stmt = $pdo->prepare("SELECT permissions FROM roles WHERE id = :role_id");
    $stmt->execute(['role_id' => $_SESSION['user_role_id']]);
    $role = $stmt->fetch();

    if ($role) {
        $permissions = json_decode($role['permissions'], true);

        // Check if user has the specific permission or all permissions
        if (isset($permissions['all']) && $permissions['all'] === true) {
            return true;
        }

        return isset($permissions[$permission]) && $permissions[$permission] === true;
    }

    return false;
}

/**
 * Get user by ID
 *
 * @param int $id User ID
 * @return array|false User data or false if not found
 */
function getUserById($id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT u.*, r.name as role FROM users u
                          JOIN roles r ON u.role_id = r.id
                          WHERE u.id = :id");
    $stmt->execute(['id' => $id]);

    return $stmt->fetch();
}

/**
 * Get all stores
 *
 * @return array Array of stores
 */
function getAllStores() {
    global $pdo;

    $stmt = $pdo->query("SELECT * FROM stores ORDER BY name");
    return $stmt->fetchAll();
}

/**
 * Get store by ID
 *
 * @param int $id Store ID
 * @return array|false Store data or false if not found
 */
function getStoreById($id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM stores WHERE id = :id");
    $stmt->execute(['id' => $id]);

    return $stmt->fetch();
}

/**
 * Get all templates
 *
 * @return array Array of templates
 */
function getAllTemplates() {
    global $pdo;

    $stmt = $pdo->query("SELECT * FROM templates ORDER BY name");
    return $stmt->fetchAll();
}

/**
 * Get template by ID with sections and items
 *
 * @param int $id Template ID
 * @return array|false Template data with sections and items or false if not found
 */
function getTemplateById($id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM templates WHERE id = :id");
    $stmt->execute(['id' => $id]);
    $template = $stmt->fetch();

    if ($template) {
        // Get sections
        $stmt = $pdo->prepare("SELECT * FROM template_sections WHERE template_id = :template_id ORDER BY order_num");
        $stmt->execute(['template_id' => $id]);
        $template['sections'] = $stmt->fetchAll();

        // Get items for each section
        foreach ($template['sections'] as &$section) {
            $stmt = $pdo->prepare("SELECT * FROM template_items WHERE section_id = :section_id ORDER BY order_num");
            $stmt->execute(['section_id' => $section['id']]);
            $section['items'] = $stmt->fetchAll();
        }
    }

    return $template;
}

/**
 * Get audit by ID with items
 *
 * @param int $id Audit ID
 * @return array|false Audit data with items or false if not found
 */
function getAuditById($id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT a.*, s.name as store_name, t.name as template_name, u.username as auditor
                          FROM audits a
                          JOIN stores s ON a.store_id = s.id
                          JOIN templates t ON a.template_id = t.id
                          JOIN users u ON a.user_id = u.id
                          WHERE a.id = :id");
    $stmt->execute(['id' => $id]);
    $audit = $stmt->fetch();

    if ($audit) {
        // Get template structure
        $template = getTemplateById($audit['template_id']);
        $audit['template'] = $template;

        // Get audit items
        $stmt = $pdo->prepare("SELECT * FROM audit_items WHERE audit_id = :audit_id");
        $stmt->execute(['audit_id' => $id]);
        $auditItems = $stmt->fetchAll();

        // Organize audit items by template_item_id for easy access
        $itemsById = [];
        foreach ($auditItems as $item) {
            // Get images for this item
            $stmt = $pdo->prepare("SELECT * FROM audit_item_images WHERE audit_item_id = :audit_item_id");
            $stmt->execute(['audit_item_id' => $item['id']]);
            $images = $stmt->fetchAll();

            $item['images'] = $images;
            $itemsById[$item['template_item_id']] = $item;
        }
        $audit['items'] = $itemsById;
    }

    return $audit;
}

/**
 * Get completed audits with filtering
 *
 * @param array $filters Filters to apply
 * @param int $limit Number of records to return
 * @param int $offset Offset for pagination
 * @return array Array of audits
 */
function getCompletedAudits($filters = [], $limit = 10, $offset = 0) {
    global $pdo;

    $sql = "SELECT a.*, s.name as store_name, u.username as auditor
            FROM audits a
            JOIN stores s ON a.store_id = s.id
            JOIN users u ON a.user_id = u.id
            WHERE a.status = 'completed'";
    $params = [];

    // Apply filters
    if (!empty($filters['store_id'])) {
        $sql .= " AND a.store_id = :store_id";
        $params['store_id'] = $filters['store_id'];
    }

    if (!empty($filters['min_score'])) {
        $sql .= " AND a.total_score >= :min_score";
        $params['min_score'] = $filters['min_score'];
    }

    if (!empty($filters['max_score'])) {
        $sql .= " AND a.total_score <= :max_score";
        $params['max_score'] = $filters['max_score'];
    }

    if (!empty($filters['start_date'])) {
        $sql .= " AND DATE(a.completed_at) >= :start_date";
        $params['start_date'] = $filters['start_date'];
    }

    if (!empty($filters['end_date'])) {
        $sql .= " AND DATE(a.completed_at) <= :end_date";
        $params['end_date'] = $filters['end_date'];
    }

    $sql .= " ORDER BY a.completed_at DESC LIMIT :limit OFFSET :offset";
    $params['limit'] = $limit;
    $params['offset'] = $offset;

    $stmt = $pdo->prepare($sql);

    // Bind parameters
    foreach ($params as $key => $value) {
        if ($key == 'limit' || $key == 'offset') {
            $stmt->bindValue(":$key", $value, PDO::PARAM_INT);
        } else {
            $stmt->bindValue(":$key", $value);
        }
    }

    $stmt->execute();
    return $stmt->fetchAll();
}

/**
 * Count completed audits with filtering
 *
 * @param array $filters Filters to apply
 * @return int Number of audits
 */
function countCompletedAudits($filters = []) {
    global $pdo;

    $sql = "SELECT COUNT(*) as count
            FROM audits a
            JOIN stores s ON a.store_id = s.id
            WHERE a.status = 'completed'";
    $params = [];

    // Apply filters
    if (!empty($filters['store_id'])) {
        $sql .= " AND a.store_id = :store_id";
        $params['store_id'] = $filters['store_id'];
    }

    if (!empty($filters['min_score'])) {
        $sql .= " AND a.total_score >= :min_score";
        $params['min_score'] = $filters['min_score'];
    }

    if (!empty($filters['max_score'])) {
        $sql .= " AND a.total_score <= :max_score";
        $params['max_score'] = $filters['max_score'];
    }

    if (!empty($filters['start_date'])) {
        $sql .= " AND DATE(a.completed_at) >= :start_date";
        $params['start_date'] = $filters['start_date'];
    }

    if (!empty($filters['end_date'])) {
        $sql .= " AND DATE(a.completed_at) <= :end_date";
        $params['end_date'] = $filters['end_date'];
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $result = $stmt->fetch();

    return $result['count'];
}

/**
 * Get dashboard statistics
 *
 * @return array Statistics data
 */
function getDashboardStats() {
    global $pdo;

    $stats = [];

    // Total stores
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM stores");
    $stats['total_stores'] = $stmt->fetch()['count'];

    // Total audits (only completed ones)
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM audits WHERE status = 'completed'");
    $stats['total_audits'] = $stmt->fetch()['count'];

    // Completed audits
    $stats['completed_audits'] = $stats['total_audits'];

    // In progress audits
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM audits WHERE status = 'in_progress'");
    $stats['in_progress_audits'] = $stmt->fetch()['count'];

    // Average score
    $stmt = $pdo->query("SELECT AVG(total_score) as avg_score FROM audits WHERE status = 'completed'");
    $stats['avg_score'] = $stmt->fetch()['avg_score'] ?? 0;

    // Upcoming visits
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM visit_plans WHERE visit_date >= CURDATE()");
    $stmt->execute();
    $stats['upcoming_visits'] = $stmt->fetch()['count'];

    return $stats;
}
