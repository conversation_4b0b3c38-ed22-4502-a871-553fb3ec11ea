<?php
// Bu skript istifadəçi şifrələrini düzgün şəkildə hash edir

require_once 'config.php';
require_once 'functions.php';

// Düzəltmə haqqında məlumat
echo "<h1>Şifrə düzəltmə skripti</h1>";
echo "<p>Bu skript istifadəçi şifrələrini düzgün şəkildə hash edir.</p>";

try {
    global $pdo;
    
    // Admin şifrəsini yenilə
    $admin_password = 'Admin123';
    $admin_hash = password_hash($admin_password, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE username = 'Admin'");
    $stmt->execute(['password' => $admin_hash]);
    echo "<p>Admin şifrəsi yeniləndi.</p>";
    
    // Audit şifrəsini yenilə
    $audit_password = 'Audit123';
    $audit_hash = password_hash($audit_password, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE username = 'Audit'");
    $stmt->execute(['password' => $audit_hash]);
    echo "<p>Audit şifrəsi yeniləndi.</p>";
    
    // Menecer şifrəsini yenilə
    $menecer_password = 'Menecer123';
    $menecer_hash = password_hash($menecer_password, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE username = 'Menecer'");
    $stmt->execute(['password' => $menecer_hash]);
    echo "<p>Menecer şifrəsi yeniləndi.</p>";
    
    echo "<p style='color: green;'>Bütün şifrələr uğurla yeniləndi!</p>";
    echo "<p><a href='index.php'>Giriş səhifəsinə qayıt</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Xəta baş verdi: " . $e->getMessage() . "</p>";
}
?>
