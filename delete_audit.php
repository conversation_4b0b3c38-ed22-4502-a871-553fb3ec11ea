<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and admin role
requireLogin();

// Only Admin can delete completed audits
if ($_SESSION['user_role'] !== 'Admin') {
    flash('error_message', 'Bu əməliyyat üçün icazəniz yoxdur', 'alert alert-danger');
    redirect('completed.php');
}

// Get audit ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış yoxlama ID', 'alert alert-danger');
    redirect('completed.php');
}

// Get audit data
$audit = getAuditById($id);

if (!$audit) {
    flash('error_message', 'Yoxlama tapılmadı', 'alert alert-danger');
    redirect('completed.php');
}

// Check if audit is completed
if ($audit['status'] !== 'completed') {
    flash('error_message', 'Yalnız tamamlanmış yoxlamalar silinə bilər', 'alert alert-danger');
    redirect('completed.php');
}

// Process deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
    global $pdo;
    
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Get all audit items
        $stmt = $pdo->prepare("SELECT id FROM audit_items WHERE audit_id = :audit_id");
        $stmt->execute(['audit_id' => $id]);
        $auditItems = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Delete images for each audit item
        foreach ($auditItems as $auditItemId) {
            // Get image paths
            $stmt = $pdo->prepare("SELECT image FROM audit_item_images WHERE audit_item_id = :audit_item_id");
            $stmt->execute(['audit_item_id' => $auditItemId]);
            $images = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Delete image files
            foreach ($images as $image) {
                $imagePath = getImageFullPath($image);
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }
            
            // Delete image records
            $stmt = $pdo->prepare("DELETE FROM audit_item_images WHERE audit_item_id = :audit_item_id");
            $stmt->execute(['audit_item_id' => $auditItemId]);
        }
        
        // Delete audit items
        $stmt = $pdo->prepare("DELETE FROM audit_items WHERE audit_id = :audit_id");
        $stmt->execute(['audit_id' => $id]);
        
        // Delete audit
        $stmt = $pdo->prepare("DELETE FROM audits WHERE id = :id");
        $stmt->execute(['id' => $id]);
        
        // Commit transaction
        $pdo->commit();
        
        flash('success_message', 'Yoxlama uğurla silindi');
        redirect('completed.php');
    } catch (PDOException $e) {
        // Rollback transaction on error
        $pdo->rollBack();
        flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
        redirect('completed.php');
    }
}

// Include header
include 'header.php';
?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">Yoxlamanı silmək</h5>
                </div>
                <div class="card-body">
                    <p class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> Diqqət! Bu əməliyyat geri qaytarıla bilməz.
                    </p>
                    
                    <p>Siz <strong><?php echo $audit['store_name']; ?></strong> mağazası üçün <strong><?php echo date('d.m.Y', strtotime($audit['created_at'])); ?></strong> tarixində yaradılmış yoxlamanı silmək istəyirsiniz.</p>
                    
                    <p>Bu yoxlama ilə bağlı bütün məlumatlar, o cümlədən şəkillər və qeydlər silinəcək.</p>
                    
                    <form action="delete_audit.php?id=<?php echo $id; ?>" method="post" class="mt-4">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="completed.php" class="btn btn-secondary me-md-2">Ləğv et</a>
                            <button type="submit" name="confirm" value="yes" class="btn btn-danger">Sil</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'footer.php'; ?>
