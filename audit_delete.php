<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login
requireLogin();

// Get audit ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış yoxlama ID', 'alert alert-danger');
    redirect('audits.php');
}

// Get audit data
global $pdo;
$stmt = $pdo->prepare("SELECT * FROM audits WHERE id = :id");
$stmt->execute(['id' => $id]);
$audit = $stmt->fetch();

if (!$audit) {
    flash('error_message', 'Yoxlama tapılmadı', 'alert alert-danger');
    redirect('audits.php');
}

// Check if user has permission to delete this audit
if (!hasPermission('delete_audit') && $audit['user_id'] != $_SESSION['user_id']) {
    flash('error_message', 'Bu yoxlamanı silmək üçün icazəniz yoxdur', 'alert alert-danger');
    redirect('audits.php');
}

// Delete audit
try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Delete audit items
    $stmt = $pdo->prepare("DELETE FROM audit_items WHERE audit_id = :audit_id");
    $stmt->execute(['audit_id' => $id]);
    
    // Delete audit
    $stmt = $pdo->prepare("DELETE FROM audits WHERE id = :id");
    $stmt->execute(['id' => $id]);
    
    // Commit transaction
    $pdo->commit();
    
    flash('success_message', 'Yoxlama uğurla silindi');
} catch (PDOException $e) {
    // Rollback transaction on error
    $pdo->rollBack();
    flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
}

redirect('audits.php');
