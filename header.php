<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Check if user is logged in
$isLoggedIn = isLoggedIn();
?>
<!DOCTYPE html>
<html lang="az">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $app_name; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <?php if ($isLoggedIn): ?>
    <div class="main-container">
        <!-- Top Navbar -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="dashboard.php"><?php echo $app_name; ?></a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="dashboard.php">
                                <i class="fas fa-home"></i> Ana səhifə
                            </a>
                        </li>

                        <?php if (hasPermission('view_stores')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'stores.php' ? 'active' : ''; ?>" href="stores.php">
                                <i class="fas fa-store"></i> Mağazalar
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_audits') || hasPermission('create_audit')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'audits.php' ? 'active' : ''; ?>" href="audits.php">
                                <i class="fas fa-clipboard-check"></i> Yoxlamalar
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_completed')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'completed.php' ? 'active' : ''; ?>" href="completed.php">
                                <i class="fas fa-check-double"></i> Tamamlanmışlar
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('manage_visit_plan')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'visit_plan.php' ? 'active' : ''; ?>" href="visit_plan.php">
                                <i class="fas fa-calendar-alt"></i> Ziyarət planı
                            </a>
                        </li>
                        <?php endif; ?>

                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>" href="settings.php">
                                <i class="fas fa-cog"></i> Tənzimləmələr
                            </a>
                        </li>
                    </ul>

                    <div class="navbar-nav">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user"></i> <?php echo $_SESSION['user_username']; ?> (<?php echo $_SESSION['user_role']; ?>)
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Çıxış</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Flash Messages -->
        <div class="container-fluid mt-3">
            <?php flash('success_message'); ?>
            <?php flash('error_message', '', 'alert alert-danger'); ?>
            <?php flash('info_message', '', 'alert alert-info'); ?>
        </div>

        <!-- Main Content Container -->
        <div class="container-fluid mt-3">
    <?php else: ?>
    <div class="login-container">
        <!-- Flash Messages for Login Page -->
        <?php flash('login_message'); ?>
        <?php flash('register_message'); ?>
    <?php endif; ?>
