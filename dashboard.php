<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login
requireLogin();

// Get dashboard statistics
$stats = getDashboardStats();

// Include header
include 'header.php';
?>

<h1 class="mb-4">Ana səhifə</h1>

<div class="row">
    <!-- 1. Mağazalar -->
    <div class="col-md-4 col-sm-6 mb-4">
        <a href="stores.php" class="text-decoration-none">
            <div class="card dashboard-card bg-primary text-white">
                <div class="card-body">
                    <div class="card-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <h5 class="card-title">Mağazalar</h5>
                    <div class="card-value"><?php echo $stats['total_stores']; ?></div>
                </div>
            </div>
        </a>
    </div>

    <!-- 2. <PERSON><PERSON><PERSON> edən yoxlamalar -->
    <div class="col-md-4 col-sm-6 mb-4">
        <a href="audits.php" class="text-decoration-none">
            <div class="card dashboard-card bg-warning text-dark">
                <div class="card-body">
                    <div class="card-icon">
                        <i class="fas fa-spinner"></i>
                    </div>
                    <h5 class="card-title">Davam edən yoxlamalar</h5>
                    <div class="card-value"><?php echo $stats['in_progress_audits']; ?></div>
                </div>
            </div>
        </a>
    </div>

    <!-- 3. Tamamlanmış yoxlamalar -->
    <div class="col-md-4 col-sm-6 mb-4">
        <a href="completed.php" class="text-decoration-none">
            <div class="card dashboard-card bg-success text-white">
                <div class="card-body">
                    <div class="card-icon">
                        <i class="fas fa-check-double"></i>
                    </div>
                    <h5 class="card-title">Tamamlanmış yoxlamalar</h5>
                    <div class="card-value"><?php echo $stats['completed_audits']; ?></div>
                </div>
            </div>
        </a>
    </div>

    <!-- 4. Orta xal -->
    <div class="col-md-4 col-sm-6 mb-4">
        <a href="completed.php" class="text-decoration-none">
            <div class="card dashboard-card bg-danger text-white">
                <div class="card-body">
                    <div class="card-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h5 class="card-title">Orta xal</h5>
                    <div class="card-value"><?php echo number_format($stats['avg_score'], 2); ?></div>
                </div>
            </div>
        </a>
    </div>

    <!-- 5. Gələcək ziyarətlər -->
    <div class="col-md-4 col-sm-6 mb-4">
        <a href="visit_plan.php" class="text-decoration-none">
            <div class="card dashboard-card bg-info text-white">
                <div class="card-body">
                    <div class="card-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h5 class="card-title">Gələcək ziyarətlər</h5>
                    <div class="card-value"><?php echo $stats['upcoming_visits']; ?></div>
                </div>
            </div>
        </a>
    </div>

    <!-- 6. Tənzimləmələr -->
    <div class="col-md-4 col-sm-6 mb-4">
        <a href="settings.php" class="text-decoration-none">
            <div class="card dashboard-card bg-secondary text-white">
                <div class="card-body">
                    <div class="card-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h5 class="card-title">Tənzimləmələr</h5>
                    <div class="card-value"><i class="fas fa-wrench"></i></div>
                </div>
            </div>
        </a>
    </div>
</div>

<?php include 'footer.php'; ?>
