/* General Styles */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Main Container */
.main-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Navbar */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
}

.nav-link {
    padding: 0.5rem 1rem;
}

.nav-link i {
    margin-right: 5px;
}

.nav-link.active {
    font-weight: bold;
}

/* Footer */
.footer {
    margin-top: auto;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

/* Login Page */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f8f9fa;
}

.login-form {
    width: 100%;
    max-width: 400px;
    padding: 30px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.login-form h2 {
    text-align: center;
    margin-bottom: 30px;
}

/* Dashboard Cards */
.dashboard-card {
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
    height: 100%;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-card .card-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
}

.dashboard-card .card-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.dashboard-card .card-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.dashboard-card .card-value {
    font-size: 2rem;
    font-weight: bold;
}

/* Tables */
.table-container {
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.table-actions {
    white-space: nowrap;
}

.table-actions .btn {
    margin-right: 5px;
}

/* Forms */
.form-container {
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

/* Audit Checklist */
.checklist-section {
    background: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.checklist-section-header {
    background: #007bff;
    color: #fff;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.checklist-section .table {
    background-color: #fff;
}

.checklist-section .table th {
    background-color: #f0f0f0;
    font-weight: 600;
}

.checklist-section .table td {
    vertical-align: middle;
}

.section-name-header {
    background-color: #007bff !important;
    color: white;
    text-align: center;
    padding: 10px;
    border-radius: 5px;
}

.section-score {
    font-size: 1rem;
    padding: 5px 10px;
}

/* Hide the second header row but keep it for structure */
.d-none {
    display: none !important;
}

/* Make sure table headers align properly */
.checklist-section .table th {
    text-align: center;
    vertical-align: middle;
}

.image-row {
    background-color: #f9f9f9;
}

.form-check-inline {
    margin-right: 10px;
}

.image-preview {
    max-width: 200px;
    max-height: 200px;
    margin-top: 10px;
}

/* Visit Plan Calendar */
.calendar-container {
    margin-bottom: 20px;
}

.calendar-day {
    height: 120px;
    padding: 5px;
    overflow-y: auto;
    position: relative;
    vertical-align: top;
}

.calendar-day.today {
    background-color: #f8f9fa;
}

.calendar-day.bg-light {
    background-color: #f8f9fa;
}

.calendar-day-header {
    text-align: right;
    font-weight: bold;
    margin-bottom: 8px;
    color: #495057;
    font-size: 1.1rem;
}

.today .calendar-day-header {
    color: #007bff;
}

.calendar-event {
    background: #007bff;
    color: #fff;
    border-radius: 4px;
    padding: 5px 8px;
    margin-bottom: 5px;
    font-size: 0.85rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.calendar-event span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.calendar-event a {
    margin-left: 5px;
}

.date-selector .dropdown-item.active {
    background-color: #007bff;
    color: white;
}

.date-selector .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
}

/* Camera and Gallery buttons */
.camera-btn, .gallery-btn {
    border-radius: 0;
}

.camera-btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.gallery-btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.image-preview {
    max-height: 100px;
    overflow-y: auto;
}

.selected-file {
    padding: 2px 5px;
    background-color: #f8f9fa;
    border-radius: 3px;
}

.additional-input {
    border-left: 3px solid #e9ecef;
    padding-left: 10px;
    margin-left: 5px;
}

.additional-input .btn-group {
    margin-bottom: 8px;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

/* Filters */
.filters-container {
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

/* Buttons */
.btn-action {
    margin-right: 5px;
}

/* Flash Messages */
#msg-flash {
    margin-bottom: 20px;
}
