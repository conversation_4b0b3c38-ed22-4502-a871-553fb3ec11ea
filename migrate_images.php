<?php
/**
 * This script migrates existing images to the new directory structure and optimizes them
 */

require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and admin role
requireLogin();

if ($_SESSION['user_role'] !== 'Admin') {
    flash('error_message', 'Bu səhifəyə giriş icazəniz yoxdur', 'alert alert-danger');
    redirect('dashboard.php');
}

// Set time limit to 0 (no limit) for long-running script
set_time_limit(0);

// Start HTML output
?>
<!DOCTYPE html>
<html lang="az">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Şəkilləri köçürmə</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Şəkilləri köçürmə və optimallaşdırma</h1>
        <p>Bu skript mövcud şəkilləri yeni qovluq strukturuna köçürür və optimallaşdırır.</p>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Nəticələr</h5>
            </div>
            <div class="card-body">
                <?php
                // Get all images from database
                global $pdo;
                $stmt = $pdo->query("SELECT ai.id, ai.audit_item_id, ai.image FROM audit_item_images ai");
                $images = $stmt->fetchAll();
                
                echo "<p>Tapılan şəkillər: " . count($images) . "</p>";
                
                $successCount = 0;
                $errorCount = 0;
                $skippedCount = 0;
                
                // Process each image
                foreach ($images as $image) {
                    // Check if image already has a directory structure (contains /)
                    if (strpos($image['image'], '/') !== false) {
                        echo "<div class='alert alert-info'>Şəkil artıq yeni strukturdadır: {$image['image']} - keçilir</div>";
                        $skippedCount++;
                        continue;
                    }
                    
                    // Get full path of the image
                    $oldPath = UPLOAD_PATH . '/' . $image['image'];
                    
                    // Check if file exists
                    if (!file_exists($oldPath)) {
                        echo "<div class='alert alert-warning'>Şəkil tapılmadı: {$oldPath}</div>";
                        $errorCount++;
                        continue;
                    }
                    
                    // Create a temporary file array
                    $tempFile = [
                        'name' => $image['image'],
                        'type' => mime_content_type($oldPath),
                        'tmp_name' => $oldPath,
                        'error' => 0,
                        'size' => filesize($oldPath)
                    ];
                    
                    // Get audit ID and item ID from image name
                    $parts = explode('_', $image['image']);
                    $prefix = '';
                    if (count($parts) >= 4) {
                        $prefix = $parts[0] . '_' . $parts[1] . '_' . $parts[2] . '_' . $parts[3];
                    } else {
                        $prefix = 'audit_item_' . $image['audit_item_id'];
                    }
                    
                    // Upload and optimize image
                    $newPath = uploadAndOptimizeImage($tempFile, $prefix);
                    
                    if ($newPath) {
                        // Update image path in database
                        $stmt = $pdo->prepare("UPDATE audit_item_images SET image = :image WHERE id = :id");
                        $stmt->execute([
                            'image' => $newPath,
                            'id' => $image['id']
                        ]);
                        
                        echo "<div class='alert alert-success'>Şəkil uğurla köçürüldü: {$image['image']} -> {$newPath}</div>";
                        $successCount++;
                        
                        // Delete old image
                        unlink($oldPath);
                    } else {
                        echo "<div class='alert alert-danger'>Şəkil köçürülə bilmədi: {$image['image']}</div>";
                        $errorCount++;
                    }
                }
                
                echo "<div class='alert alert-primary mt-3'>";
                echo "<p><strong>Ümumi nəticə:</strong></p>";
                echo "<p>Uğurla köçürülən şəkillər: {$successCount}</p>";
                echo "<p>Keçilən şəkillər: {$skippedCount}</p>";
                echo "<p>Xəta baş verən şəkillər: {$errorCount}</p>";
                echo "</div>";
                ?>
                
                <div class="mt-3">
                    <a href="dashboard.php" class="btn btn-primary">Ana səhifəyə qayıt</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
