<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('view_stores');

// Get all stores
$stores = getAllStores();

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>Mağazalar</h1>
        <a href="dashboard.php" class="btn btn-sm btn-secondary mt-2">
            <i class="fas fa-arrow-left"></i> Ana səhifəyə qayıt
        </a>
    </div>

    <?php if (hasPermission('create_store')): ?>
    <a href="store_form.php" class="btn btn-primary">
        <i class="fas fa-plus"></i> Yeni mağaza
    </a>
    <?php endif; ?>
</div>

<div class="table-container">
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Kod</th>
                    <th>Ad</th>
                    <th>Ünvan</th>
                    <th>Telefon</th>
                    <th>Menecer</th>
                    <th>Yaradılma tarixi</th>
                    <th>Əməliyyatlar</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($stores)): ?>
                <tr>
                    <td colspan="8" class="text-center">Mağaza tapılmadı</td>
                </tr>
                <?php else: ?>
                    <?php foreach ($stores as $store): ?>
                    <tr>
                        <td><?php echo $store['id']; ?></td>
                        <td><?php echo $store['store_code'] ?? '-'; ?></td>
                        <td><?php echo $store['name']; ?></td>
                        <td><?php echo $store['address'] ?? '-'; ?></td>
                        <td><?php echo $store['phone'] ?? '-'; ?></td>
                        <td><?php echo $store['manager'] ?? '-'; ?></td>
                        <td><?php echo date('d.m.Y H:i', strtotime($store['created_at'])); ?></td>
                        <td class="table-actions">
                            <a href="store_view.php?id=<?php echo $store['id']; ?>" class="btn btn-sm btn-info btn-action" data-bs-toggle="tooltip" title="Bax">
                                <i class="fas fa-eye"></i>
                            </a>

                            <?php if (hasPermission('create_audit')): ?>
                            <a href="audit_form.php?store_id=<?php echo $store['id']; ?>" class="btn btn-sm btn-success btn-action" data-bs-toggle="tooltip" title="Yoxlama yarat">
                                <i class="fas fa-clipboard-check"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (hasPermission('edit_store')): ?>
                            <a href="store_form.php?id=<?php echo $store['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Redaktə et">
                                <i class="fas fa-edit"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (hasPermission('delete_store')): ?>
                            <a href="store_delete.php?id=<?php echo $store['id']; ?>" class="btn btn-sm btn-danger btn-action btn-delete" data-bs-toggle="tooltip" title="Sil">
                                <i class="fas fa-trash"></i>
                            </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php include 'footer.php'; ?>
