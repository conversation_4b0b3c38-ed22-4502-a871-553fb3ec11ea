<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('view_audits');

// Get audit ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış yoxlama ID', 'alert alert-danger');
    redirect('audits.php');
}

// Get audit data
$audit = getAuditById($id);

if (!$audit) {
    flash('error_message', 'Yoxlama tapılmadı', 'alert alert-danger');
    redirect('audits.php');
}

// Check if user has permission to edit this audit
// If audit is completed, only Admin can edit it
if ($audit['status'] == 'completed') {
    $canEdit = $_SESSION['user_role'] === 'Admin';
} else {
    $canEdit = hasPermission('edit_audit') || $audit['user_id'] == $_SESSION['user_id'];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $canEdit) {
    global $pdo;

    try {
        // Start transaction
        $pdo->beginTransaction();

        // Update audit items
        foreach ($audit['template']['sections'] as $section) {
            foreach ($section['items'] as $item) {
                $itemId = $item['id'];
                $status = isset($_POST['status'][$itemId]) ? sanitize($_POST['status'][$itemId]) : 'not_checked';
                $score = isset($_POST['score'][$itemId]) ? (int)$_POST['score'][$itemId] : null;
                $notes = isset($_POST['notes'][$itemId]) ? sanitize($_POST['notes'][$itemId]) : null;

                // Handle image uploads
                if (isset($_FILES['images']) && isset($_FILES['images']['name'][$itemId]) && !empty($_FILES['images']['name'][$itemId][0])) {
                    // Get audit item ID
                    $auditItemId = 0;
                    if (isset($audit['items'][$itemId])) {
                        $auditItemId = $audit['items'][$itemId]['id'];
                    } else {
                        // Insert audit item first to get ID
                        $stmtItem = $pdo->prepare("INSERT INTO audit_items (audit_id, template_item_id, status, score, notes)
                                                VALUES (:audit_id, :template_item_id, :status, :score, :notes)");
                        $stmtItem->execute([
                            'audit_id' => $id,
                            'template_item_id' => $itemId,
                            'status' => $status,
                            'score' => $score,
                            'notes' => $notes
                        ]);
                        $auditItemId = $pdo->lastInsertId();
                    }

                    // Process each uploaded image
                    $imageCount = count($_FILES['images']['name'][$itemId]);
                    for ($i = 0; $i < $imageCount; $i++) {
                        if (!empty($_FILES['images']['name'][$itemId][$i])) {
                            // Create a file array for the current image
                            $currentFile = [
                                'name' => $_FILES['images']['name'][$itemId][$i],
                                'type' => $_FILES['images']['type'][$itemId][$i],
                                'tmp_name' => $_FILES['images']['tmp_name'][$itemId][$i],
                                'error' => $_FILES['images']['error'][$itemId][$i],
                                'size' => $_FILES['images']['size'][$itemId][$i]
                            ];

                            // Upload and optimize image
                            $imagePrefix = 'audit_' . $id . '_item_' . $itemId;
                            $newFileName = uploadAndOptimizeImage($currentFile, $imagePrefix);

                            if ($newFileName) {
                                // Save image to database
                                $stmtImage = $pdo->prepare("INSERT INTO audit_item_images (audit_item_id, image) VALUES (:audit_item_id, :image)");
                                $stmtImage->execute([
                                    'audit_item_id' => $auditItemId,
                                    'image' => $newFileName
                                ]);
                            }
                        }
                    }
                }

                // Update or insert audit item
                if (isset($audit['items'][$itemId])) {
                    $stmt = $pdo->prepare("UPDATE audit_items SET status = :status, score = :score, notes = :notes WHERE audit_id = :audit_id AND template_item_id = :template_item_id");
                    $stmt->execute([
                        'status' => $status,
                        'score' => $score,
                        'notes' => $notes,
                        'audit_id' => $id,
                        'template_item_id' => $itemId
                    ]);
                } else {
                    $stmt = $pdo->prepare("INSERT INTO audit_items (audit_id, template_item_id, status, score, notes) VALUES (:audit_id, :template_item_id, :status, :score, :notes)");
                    $stmt->execute([
                        'status' => $status,
                        'score' => $score,
                        'notes' => $notes,
                        'audit_id' => $id,
                        'template_item_id' => $itemId
                    ]);
                }
            }
        }

        // Get general notes
        $generalNotes = isset($_POST['general_notes']) ? sanitize($_POST['general_notes']) : null;

        // Calculate section scores
        $sectionScores = [];
        foreach ($audit['template']['sections'] as $section) {
            $sectionId = $section['id'];
            $sectionItems = [];
            $validScores = [];

            foreach ($section['items'] as $item) {
                $itemId = $item['id'];
                $score = isset($_POST['score'][$itemId]) && $_POST['score'][$itemId] !== '' ? (int)$_POST['score'][$itemId] : null;
                $status = isset($_POST['status'][$itemId]) ? sanitize($_POST['status'][$itemId]) : 'not_checked';

                if ($score !== null && $status != 'not_checked') {
                    $validScores[] = $score;
                }

                $sectionItems[] = [
                    'id' => $itemId,
                    'score' => $score
                ];
            }

            $sectionAvg = !empty($validScores) ? array_sum($validScores) / count($validScores) : null;
            $sectionScores[$sectionId] = [
                'items' => $sectionItems,
                'average' => $sectionAvg
            ];
        }

        // Check if completing the audit
        if (isset($_POST['complete']) && $_POST['complete'] == 1) {
            // Calculate total score based on section averages
            $totalScore = 0;
            $validSectionScores = [];

            foreach ($sectionScores as $sectionId => $sectionData) {
                if ($sectionData['average'] !== null) {
                    $validSectionScores[] = $sectionData['average'];
                }
            }

            if (!empty($validSectionScores)) {
                $totalScore = array_sum($validSectionScores) / count($validSectionScores);
            }

            // Update audit status
            $stmt = $pdo->prepare("UPDATE audits SET
                status = 'completed',
                total_score = :total_score,
                section_scores = :section_scores,
                general_notes = :general_notes,
                completed_at = NOW()
                WHERE id = :id");
            $stmt->execute([
                'total_score' => $totalScore,
                'section_scores' => json_encode($sectionScores),
                'general_notes' => $generalNotes,
                'id' => $id
            ]);

            // Commit transaction
            $pdo->commit();

            flash('success_message', 'Yoxlama uğurla tamamlandı');
            redirect('completed.php');
        } else {
            // Update audit with general notes and section scores
            $stmt = $pdo->prepare("UPDATE audits SET
                general_notes = :general_notes,
                section_scores = :section_scores
                WHERE id = :id");
            $stmt->execute([
                'general_notes' => $generalNotes,
                'section_scores' => json_encode($sectionScores),
                'id' => $id
            ]);

            // Commit transaction
            $pdo->commit();

            flash('success_message', 'Yoxlama uğurla yeniləndi');
            redirect('audit_view.php?id=' . $id);
        }
    } catch (PDOException $e) {
        // Rollback transaction on error
        $pdo->rollBack();
        flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
    }
}

// Set page-specific JavaScript
$page_js = 'audit';

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Yoxlama: <?php echo $audit['store_name']; ?></h1>

    <div>
        <?php if ($audit['status'] == 'completed' && $_SESSION['user_role'] === 'Admin'): ?>
        <span class="badge bg-info me-2">Yalnız Admin tərəfindən redaktə oluna bilər</span>
        <?php endif; ?>

        <a href="<?php echo $audit['status'] == 'completed' ? 'completed.php' : 'audits.php'; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Geri
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Yoxlama məlumatları</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <p><strong>Mağaza:</strong> <?php echo $audit['store_name']; ?></p>
            </div>
            <div class="col-md-3">
                <p><strong>Şablon:</strong> <?php echo $audit['template_name']; ?></p>
            </div>
            <div class="col-md-3">
                <p><strong>Auditor:</strong> <?php echo $audit['auditor']; ?></p>
            </div>
            <div class="col-md-3">
                <p><strong>Tarix:</strong> <?php echo date('d.m.Y H:i', strtotime($audit['created_at'])); ?></p>
            </div>
        </div>
    </div>
</div>

<?php if ($canEdit): ?>
<form action="audit_view.php?id=<?php echo $id; ?>" method="post" enctype="multipart/form-data" id="audit-form">
<?php endif; ?>

<?php foreach ($audit['template']['sections'] as $section): ?>
<div class="checklist-section">
    <div class="section-name-header mb-2">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><?php echo $section['name']; ?></h5>
            <?php if ($audit['status'] == 'completed' && isset($audit['section_scores'])): ?>
                <?php
                $sectionScores = json_decode($audit['section_scores'], true);
                if (isset($sectionScores[$section['id']]) && $sectionScores[$section['id']]['average'] !== null):
                    $avgScore = round($sectionScores[$section['id']]['average'], 2);
                    $scoreClass = $avgScore >= 7 ? 'success' : ($avgScore >= 5 ? 'warning' : 'danger');
                ?>
                <span class="badge bg-<?php echo $scoreClass; ?> section-score">
                    Ortalama xal: <?php echo $avgScore; ?>
                </span>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead class="table-light">
                <tr>
                    <th width="30%">Element</th>
                    <th width="25%">Status</th>
                    <th width="25%">Qeyd</th>
                    <th width="20%">Xal</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($section['items'] as $item): ?>
                <tr>
                    <td>
                        <strong><?php echo $item['name']; ?></strong>
                    </td>
                    <td>
                        <?php if ($canEdit): ?>
                        <div class="d-flex">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="status[<?php echo $item['id']; ?>]" id="status-yes-<?php echo $item['id']; ?>" value="yes" data-item-id="<?php echo $item['id']; ?>" <?php echo isset($audit['items'][$item['id']]) && $audit['items'][$item['id']]['status'] == 'yes' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="status-yes-<?php echo $item['id']; ?>">Bəli</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="status[<?php echo $item['id']; ?>]" id="status-no-<?php echo $item['id']; ?>" value="no" data-item-id="<?php echo $item['id']; ?>" <?php echo isset($audit['items'][$item['id']]) && $audit['items'][$item['id']]['status'] == 'no' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="status-no-<?php echo $item['id']; ?>">Xeyr</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="status[<?php echo $item['id']; ?>]" id="status-not-checked-<?php echo $item['id']; ?>" value="not_checked" data-item-id="<?php echo $item['id']; ?>" <?php echo !isset($audit['items'][$item['id']]) || $audit['items'][$item['id']]['status'] == 'not_checked' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="status-not-checked-<?php echo $item['id']; ?>">Yoxlanılmayıb</label>
                            </div>
                        </div>
                        <?php else: ?>
                        <?php
                        if (isset($audit['items'][$item['id']])) {
                            switch ($audit['items'][$item['id']]['status']) {
                                case 'yes':
                                    echo '<span class="badge bg-success">Bəli</span>';
                                    break;
                                case 'no':
                                    echo '<span class="badge bg-danger">Xeyr</span>';
                                    break;
                                default:
                                    echo '<span class="badge bg-secondary">Yoxlanılmayıb</span>';
                            }
                        } else {
                            echo '<span class="badge bg-secondary">Yoxlanılmayıb</span>';
                        }
                        ?>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($canEdit): ?>
                        <textarea class="form-control form-control-sm" id="notes-<?php echo $item['id']; ?>" name="notes[<?php echo $item['id']; ?>]" rows="1"><?php echo isset($audit['items'][$item['id']]) ? $audit['items'][$item['id']]['notes'] : ''; ?></textarea>
                        <?php else: ?>
                        <?php echo isset($audit['items'][$item['id']]) && !empty($audit['items'][$item['id']]['notes']) ? $audit['items'][$item['id']]['notes'] : '-'; ?>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($canEdit && isset($item['has_score']) && $item['has_score']): ?>
                        <select class="form-select form-select-sm" name="score[<?php echo $item['id']; ?>]">
                            <option value="">Seçin</option>
                            <?php for ($i = 0; $i <= 10; $i++): ?>
                            <option value="<?php echo $i; ?>" <?php echo isset($audit['items'][$item['id']]) && $audit['items'][$item['id']]['score'] == $i ? 'selected' : ''; ?>><?php echo $i; ?></option>
                            <?php endfor; ?>
                        </select>
                        <?php elseif (!$canEdit && isset($item['has_score']) && $item['has_score']): ?>
                        <span class="badge bg-primary"><?php echo isset($audit['items'][$item['id']]) && $audit['items'][$item['id']]['score'] !== null ? $audit['items'][$item['id']]['score'] : '-'; ?></span>
                        <?php else: ?>
                        -
                        <?php endif; ?>
                    </td>
                </tr>
                <?php if ($canEdit && isset($audit['items'][$item['id']]) && $audit['items'][$item['id']]['status'] == 'no'): ?>
                <tr class="image-row">
                    <td colspan="4">
                        <div id="image-upload-container-<?php echo $item['id']; ?>">
                            <label class="form-label">Şəkillər:</label>
                            <div class="mb-2">
                                <div class="image-upload-container">
                                    <div class="mb-2">
                                        <div class="btn-group w-100 mb-2" role="group">
                                            <button type="button" class="btn btn-outline-primary camera-btn" data-item-id="<?php echo $item['id']; ?>">
                                                <i class="fas fa-camera"></i> Kameradan çək
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary gallery-btn" data-item-id="<?php echo $item['id']; ?>">
                                                <i class="fas fa-images"></i> Qalereyadan seç
                                            </button>
                                        </div>
                                        <input type="file" class="form-control image-upload d-none" name="images[<?php echo $item['id']; ?>][]" accept="image/*" multiple id="gallery-input-<?php echo $item['id']; ?>">
                                        <input type="file" class="form-control image-upload d-none" name="images[<?php echo $item['id']; ?>][]" accept="image/*" capture="environment" id="camera-input-<?php echo $item['id']; ?>">
                                        <div class="image-preview"></div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-success add-more-images" data-item-id="<?php echo $item['id']; ?>">
                                        <i class="fas fa-plus"></i> Daha çox şəkil əlavə et
                                    </button>
                                </div>
                            </div>

                            <?php if (isset($audit['items'][$item['id']]) && !empty($audit['items'][$item['id']]['images'])): ?>
                            <div class="mt-3">
                                <label class="form-label">Mövcud şəkillər:</label>
                                <div class="row">
                                    <?php foreach ($audit['items'][$item['id']]['images'] as $image): ?>
                                    <div class="col-md-4 col-sm-6 mb-3">
                                        <div class="card">
                                            <img src="<?php echo $upload_dir . $image['image']; ?>" alt="Image" class="card-img-top">
                                            <div class="card-body p-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted"><?php echo date('d.m.Y', filemtime(getImageFullPath($image['image']))); ?></small>
                                                    <a href="delete_image.php?id=<?php echo $image['id']; ?>&audit_id=<?php echo $id; ?>" class="btn btn-sm btn-danger btn-delete">
                                                        <i class="fas fa-trash"></i> Sil
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
                <?php elseif (!$canEdit && isset($audit['items'][$item['id']]) && !empty($audit['items'][$item['id']]['images'])): ?>
                <tr class="image-row">
                    <td colspan="4">
                        <div class="mt-3">
                            <label class="form-label">Şəkillər:</label>
                            <div class="row">
                                <?php foreach ($audit['items'][$item['id']]['images'] as $img): ?>
                                <div class="col-md-4 col-sm-6 mb-3">
                                    <div class="card">
                                        <img src="<?php echo $upload_dir . $img['image']; ?>" alt="Image" class="card-img-top">
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </td>
                </tr>
                <?php endif; ?>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php endforeach; ?>

<?php if ($canEdit): ?>
<div class="card mt-4 mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Ümumi qeydlər</h5>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <label for="general_notes" class="form-label">Yoxlama haqqında ümumi qeydlər:</label>
            <textarea class="form-control" id="general_notes" name="general_notes" rows="4"><?php echo isset($audit['general_notes']) ? $audit['general_notes'] : ''; ?></textarea>
        </div>
    </div>
</div>

<div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
    <button type="submit" class="btn btn-primary me-md-2">Yadda saxla</button>
    <button type="submit" class="btn btn-success" name="complete" value="1">Yoxlamanı tamamla</button>
</div>
</form>
<?php else: ?>
<?php if (!empty($audit['general_notes'])): ?>
<div class="card mt-4 mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Ümumi qeydlər</h5>
    </div>
    <div class="card-body">
        <p><?php echo nl2br(htmlspecialchars($audit['general_notes'])); ?></p>
    </div>
</div>
<?php endif; ?>
<?php endif; ?>

<?php include 'footer.php'; ?>
