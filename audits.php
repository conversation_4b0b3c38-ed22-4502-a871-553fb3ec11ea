<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('view_audits');

// Get in-progress audits
global $pdo;
$stmt = $pdo->query("SELECT a.*, s.name as store_name, t.name as template_name, u.username as auditor
                    FROM audits a
                    JOIN stores s ON a.store_id = s.id
                    JOIN templates t ON a.template_id = t.id
                    JOIN users u ON a.user_id = u.id
                    WHERE a.status = 'in_progress'
                    ORDER BY a.created_at DESC");
$audits = $stmt->fetchAll();

// Get templates for new audit
$templates = getAllTemplates();

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>Yoxlamalar</h1>
        <a href="dashboard.php" class="btn btn-sm btn-secondary mt-2">
            <i class="fas fa-arrow-left"></i> Ana səhifəyə qayıt
        </a>
    </div>

    <?php if (hasPermission('create_audit')): ?>
    <div>
        <a href="templates.php" class="btn btn-info me-2">
            <i class="fas fa-list-check"></i> Şablonlar
        </a>
        <a href="audit_form.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Yeni yoxlama
        </a>
    </div>
    <?php endif; ?>
</div>

<div class="table-container">
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Mağaza</th>
                    <th>Şablon</th>
                    <th>Auditor</th>
                    <th>Yaradılma tarixi</th>
                    <th>Əməliyyatlar</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($audits)): ?>
                <tr>
                    <td colspan="6" class="text-center">Davam edən yoxlama tapılmadı</td>
                </tr>
                <?php else: ?>
                    <?php foreach ($audits as $audit): ?>
                    <tr>
                        <td><?php echo $audit['id']; ?></td>
                        <td><?php echo $audit['store_name']; ?></td>
                        <td><?php echo $audit['template_name']; ?></td>
                        <td><?php echo $audit['auditor']; ?></td>
                        <td><?php echo date('d.m.Y H:i', strtotime($audit['created_at'])); ?></td>
                        <td class="table-actions">
                            <?php if (hasPermission('edit_audit') || $audit['user_id'] == $_SESSION['user_id']): ?>
                            <a href="audit_view.php?id=<?php echo $audit['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Davam et">
                                <i class="fas fa-edit"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (hasPermission('delete_audit') || $audit['user_id'] == $_SESSION['user_id']): ?>
                            <a href="audit_delete.php?id=<?php echo $audit['id']; ?>" class="btn btn-sm btn-danger btn-action btn-delete" data-bs-toggle="tooltip" title="Sil">
                                <i class="fas fa-trash"></i>
                            </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php include 'footer.php'; ?>
