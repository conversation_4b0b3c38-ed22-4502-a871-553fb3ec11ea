<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login
requireLogin();

// Get user data
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

// Check if admin
$isAdmin = ($_SESSION['user_role'] === 'Admin');

// If admin, get all users and roles
if ($isAdmin) {
    $users = getAllUsers();
    $roles = getAllRoles();
}

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Tənzimləmələr</h1>

    <?php if ($isAdmin): ?>
    <div>
        <a href="role_form.php" class="btn btn-info me-2">
            <i class="fas fa-user-tag"></i> Yeni rol
        </a>
        <a href="user_form.php" class="btn btn-primary">
            <i class="fas fa-user-plus"></i> Yeni istifadəçi
        </a>
    </div>
    <?php endif; ?>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <!-- User Profile Card -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Profil məlumatları</h5>
            </div>
            <div class="card-body">
                <form action="change_password.php" method="post">
                    <div class="mb-3">
                        <label for="username" class="form-label">İstifadəçi adı</label>
                        <input type="text" class="form-control" id="username" value="<?php echo $user['username']; ?>" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Rol</label>
                        <input type="text" class="form-control" id="role" value="<?php echo $user['role']; ?>" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Cari şifrə *</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">Yeni şifrə *</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Şifrəni təsdiqlə *</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Şifrəni dəyiş</button>
                </form>
            </div>
        </div>
    </div>

    <?php if ($isAdmin): ?>
    <div class="col-md-6">
        <!-- Admin Tabs -->
        <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="true">İstifadəçilər</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="roles-tab" data-bs-toggle="tab" data-bs-target="#roles" type="button" role="tab" aria-controls="roles" aria-selected="false">Rollar</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="tools-tab" data-bs-toggle="tab" data-bs-target="#tools" type="button" role="tab" aria-controls="tools" aria-selected="false">Alətlər</button>
            </li>
        </ul>

        <div class="tab-content" id="settingsTabsContent">
            <!-- Users Tab -->
            <div class="tab-pane fade show active" id="users" role="tabpanel" aria-labelledby="users-tab">
                <div class="table-container">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>İstifadəçi adı</th>
                                    <th>Rol</th>
                                    <th>Əməliyyatlar</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                <tr>
                                    <td colspan="4" class="text-center">İstifadəçi tapılmadı</td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($users as $u): ?>
                                    <tr>
                                        <td><?php echo $u['id']; ?></td>
                                        <td><?php echo $u['username']; ?></td>
                                        <td><?php echo $u['role']; ?></td>
                                        <td class="table-actions">
                                            <a href="user_form.php?id=<?php echo $u['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Redaktə et">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($u['id'] != $_SESSION['user_id']): ?>
                                            <a href="user_delete.php?id=<?php echo $u['id']; ?>" class="btn btn-sm btn-danger btn-action btn-delete" data-bs-toggle="tooltip" title="Sil">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Roles Tab -->
            <div class="tab-pane fade" id="roles" role="tabpanel" aria-labelledby="roles-tab">
                <div class="table-container">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Rol adı</th>
                                    <th>İcazələr</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($roles)): ?>
                                <tr>
                                    <td colspan="3" class="text-center">Rol tapılmadı</td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($roles as $role): ?>
                                    <tr>
                                        <td><?php echo $role['id']; ?></td>
                                        <td><?php echo $role['name']; ?></td>
                                        <td>
                                            <?php
                                            $permissions = json_decode($role['permissions'], true);

                                            if (isset($permissions['all']) && $permissions['all'] === true) {
                                                echo '<span class="badge bg-success">Bütün icazələr</span>';
                                            } else {
                                                $permissionLabels = [
                                                    'view_stores' => 'Mağazaları görmək',
                                                    'create_store' => 'Mağaza yaratmaq',
                                                    'edit_store' => 'Mağazanı redaktə etmək',
                                                    'delete_store' => 'Mağazanı silmək',
                                                    'view_audits' => 'Yoxlamaları görmək',
                                                    'create_audit' => 'Yoxlama yaratmaq',
                                                    'edit_audit' => 'Yoxlamanı redaktə etmək',
                                                    'delete_audit' => 'Yoxlamanı silmək',
                                                    'view_completed' => 'Tamamlanmış yoxlamaları görmək',
                                                    'manage_visit_plan' => 'Ziyarət planını idarə etmək'
                                                ];

                                                foreach ($permissions as $permission => $value) {
                                                    if ($value === true) {
                                                        echo '<span class="badge bg-primary me-1">' . ($permissionLabels[$permission] ?? $permission) . '</span>';
                                                    }
                                                }
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Tools Tab -->
            <div class="tab-pane fade" id="tools" role="tabpanel" aria-labelledby="tools-tab">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Admin alətləri</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="update_stores_add_code.php" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">Mağaza kodu əlavə et</h5>
                                </div>
                                <p class="mb-1">Verilənlər bazasında mağazalar cədvəlinə "store_code" sütunu əlavə edir.</p>
                            </a>

                            <a href="update_store_codes.php" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">Mağaza məlumatlarını yenilə</h5>
                                </div>
                                <p class="mb-1">Bütün mağazaların məlumatlarını toplu şəkildə yeniləmək üçün.</p>
                            </a>

                            <a href="update_audit_table.php" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">Yoxlama cədvəlini yenilə</h5>
                                </div>
                                <p class="mb-1">Verilənlər bazasında yoxlamalar cədvəlinə lazımi sütunları əlavə edir.</p>
                            </a>

                            <a href="create_template_from_md.php" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">Markdown faylından şablon yarat</h5>
                                </div>
                                <p class="mb-1">suallar.md faylından yeni yoxlama şablonu yaradır.</p>
                            </a>

                            <a href="update_template_items.php" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">Şablon elementlərini yenilə</h5>
                                </div>
                                <p class="mb-1">Verilənlər bazasında şablon elementlərinə "has_score" sütunu əlavə edir.</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include 'footer.php'; ?>
