<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('view_completed');

// Get audit ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış yoxlama ID', 'alert alert-danger');
    redirect('completed.php');
}

// Get audit data
$audit = getAuditById($id);

if (!$audit) {
    flash('error_message', 'Yoxlama tapılmadı', 'alert alert-danger');
    redirect('completed.php');
}

// Check if audit is completed
if ($audit['status'] !== 'completed') {
    flash('error_message', 'Yalnız tamamlanmış yoxlamaları yükləmək olar', 'alert alert-danger');
    redirect('audits.php');
}

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="audit_report_' . $id . '.csv"');

// Create a file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add UTF-8 BOM
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Add header row
fputcsv($output, ['Yoxlama ID', 'Mağaza', 'Şablon', 'Auditor', 'Yaradılma tarixi', 'Tamamlanma tarixi', 'Ümumi xal']);

// Add audit info
fputcsv($output, [
    $audit['id'],
    $audit['store_name'],
    $audit['template_name'],
    $audit['auditor'],
    date('d.m.Y H:i', strtotime($audit['created_at'])),
    date('d.m.Y H:i', strtotime($audit['completed_at'])),
    number_format($audit['total_score'], 2)
]);

// Add empty row
fputcsv($output, []);

// Add header for items
fputcsv($output, ['Bölmə', 'Element', 'Status', 'Xal', 'Qeyd', 'Şəkillər']);

// Add items
foreach ($audit['template']['sections'] as $section) {
    foreach ($section['items'] as $item) {
        $status = 'Yoxlanılmayıb';
        $score = '';
        $notes = '';
        $images = '';

        if (isset($audit['items'][$item['id']])) {
            switch ($audit['items'][$item['id']]['status']) {
                case 'yes':
                    $status = 'Bəli';
                    break;
                case 'no':
                    $status = 'Xeyr';
                    break;
            }

            $score = $audit['items'][$item['id']]['score'] ?? '';
            $notes = $audit['items'][$item['id']]['notes'] ?? '';

            // Add image information
            if (!empty($audit['items'][$item['id']]['images'])) {
                $imageCount = count($audit['items'][$item['id']]['images']);
                $images = $imageCount . ' şəkil';
            }
        }

        fputcsv($output, [
            $section['name'],
            $item['name'],
            $status,
            $score,
            $notes,
            $images
        ]);
    }
}

// Close the file pointer
fclose($output);
