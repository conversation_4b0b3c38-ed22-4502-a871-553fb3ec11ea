<nav id="sidebar">
    <div class="sidebar-header">
        <h3><?php echo $app_name; ?></h3>
    </div>

    <ul class="list-unstyled components">
        <li <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'class="active"' : ''; ?>>
            <a href="dashboard.php">
                <i class="fas fa-home"></i>
                Ana səhifə
            </a>
        </li>
        
        <?php if (hasPermission('view_stores')): ?>
        <li <?php echo basename($_SERVER['PHP_SELF']) == 'stores.php' ? 'class="active"' : ''; ?>>
            <a href="stores.php">
                <i class="fas fa-store"></i>
                Mağazalar
            </a>
        </li>
        <?php endif; ?>
        
        <?php if (hasPermission('view_audits') || hasPermission('create_audit')): ?>
        <li <?php echo basename($_SERVER['PHP_SELF']) == 'audits.php' ? 'class="active"' : ''; ?>>
            <a href="audits.php">
                <i class="fas fa-clipboard-check"></i>
                Yoxlamalar
            </a>
        </li>
        <?php endif; ?>
        
        <?php if (hasPermission('view_completed')): ?>
        <li <?php echo basename($_SERVER['PHP_SELF']) == 'completed.php' ? 'class="active"' : ''; ?>>
            <a href="completed.php">
                <i class="fas fa-check-double"></i>
                Tamamlanmışlar
            </a>
        </li>
        <?php endif; ?>
        
        <?php if (hasPermission('manage_visit_plan')): ?>
        <li <?php echo basename($_SERVER['PHP_SELF']) == 'visit_plan.php' ? 'class="active"' : ''; ?>>
            <a href="visit_plan.php">
                <i class="fas fa-calendar-alt"></i>
                Ziyarət planı
            </a>
        </li>
        <?php endif; ?>
        
        <?php if ($_SESSION['user_role'] === 'Admin'): ?>
        <li <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'class="active"' : ''; ?>>
            <a href="settings.php">
                <i class="fas fa-cog"></i>
                Tənzimləmələr
            </a>
        </li>
        <?php endif; ?>
    </ul>
</nav>
