<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('create_audit');

// Get all templates
$templates = getAllTemplates();

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Şablonlar</h1>
    
    <div>
        <a href="audits.php" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left"></i> Geri
        </a>
        <a href="template_form.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Yeni şablon
        </a>
    </div>
</div>

<div class="table-container">
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Ad</th>
                    <th><PERSON><PERSON><PERSON><PERSON> tarixi</th>
                    <th>Əməliyyatlar</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($templates)): ?>
                <tr>
                    <td colspan="4" class="text-center">Şablon tapılmadı</td>
                </tr>
                <?php else: ?>
                    <?php foreach ($templates as $template): ?>
                    <tr>
                        <td><?php echo $template['id']; ?></td>
                        <td><?php echo $template['name']; ?></td>
                        <td><?php echo date('d.m.Y H:i', strtotime($template['created_at'])); ?></td>
                        <td class="table-actions">
                            <a href="template_view.php?id=<?php echo $template['id']; ?>" class="btn btn-sm btn-info btn-action" data-bs-toggle="tooltip" title="Bax">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="template_form.php?id=<?php echo $template['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Redaktə et">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="template_delete.php?id=<?php echo $template['id']; ?>" class="btn btn-sm btn-danger btn-action btn-delete" data-bs-toggle="tooltip" title="Sil">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php include 'footer.php'; ?>
