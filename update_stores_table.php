<?php
// Bu skript stores cədvəlinə yeni sütunlar əlavə edir

require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and admin role
requireLogin();

if ($_SESSION['user_role'] !== 'Admin') {
    flash('error_message', 'Bu səhifəyə giriş icazəniz yoxdur', 'alert alert-danger');
    redirect('dashboard.php');
}

// Düzəltmə haqqında məlumat
echo "<h1>Mağazalar cədvəlini yeniləmə skripti</h1>";
echo "<p>Bu skript stores cədvəlinə yeni sütunlar əlavə edir.</p>";

try {
    global $pdo;
    
    // Yeni sütunları əlavə et
    $sql = "ALTER TABLE stores 
            ADD COLUMN IF NOT EXISTS phone VARCHAR(50) DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS email VARCHAR(100) DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS manager VARCHAR(100) DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS notes TEXT DEFAULT NULL";
    
    $pdo->exec($sql);
    
    echo "<p style='color: green;'>Mağazalar cədvəli uğurla yeniləndi!</p>";
    echo "<p><a href='stores.php'>Mağazalar səhifəsinə qayıt</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Xəta baş verdi: " . $e->getMessage() . "</p>";
}
?>
