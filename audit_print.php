<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('view_completed');

// Get audit ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış yoxlama ID', 'alert alert-danger');
    redirect('completed.php');
}

// Get audit data
$audit = getAuditById($id);

if (!$audit) {
    flash('error_message', 'Yoxlama tapılmadı', 'alert alert-danger');
    redirect('completed.php');
}

// Check if audit is completed
if ($audit['status'] !== 'completed') {
    flash('error_message', 'Yalnız tamamlanmış yoxlamaları çap etmək olar', 'alert alert-danger');
    redirect('audits.php');
}
?>
<!DOCTYPE html>
<html lang="az">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yoxlama hesabatı - <?php echo $audit['store_name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .report-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .report-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .report-info {
            margin-bottom: 20px;
        }

        .report-info-item {
            margin-bottom: 5px;
        }

        .section-header {
            background-color: #007bff;
            color: white;
            padding: 10px;
            margin-top: 20px;
            margin-bottom: 10px;
            font-weight: bold;
            border-radius: 5px;
        }

        .table {
            width: 100%;
            margin-bottom: 1rem;
            color: #212529;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            vertical-align: middle;
            border: 1px solid #dee2e6;
        }

        .table thead th {
            background-color: #f0f0f0;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
        }

        .section-name-header {
            background-color: #007bff !important;
            color: white;
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        /* Hide the second header row but keep it for structure */
        .d-none {
            display: none !important;
        }

        /* Make sure table headers align properly */
        .table th {
            text-align: center;
            vertical-align: middle;
        }

        @media print {
            .section-name-header {
                background-color: #007bff !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }

        .item-image {
            max-width: 200px;
            max-height: 200px;
            margin-top: 10px;
        }

        .report-footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }

        @media print {
            body {
                margin: 0;
                padding: 15px;
            }

            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="no-print mb-3">
        <button class="btn btn-primary" onclick="window.print()">Çap et</button>
        <a href="completed.php" class="btn btn-secondary">Geri</a>
    </div>

    <div class="report-header">
        <div class="report-title">Yoxlama hesabatı</div>
        <div>Mağaza: <?php echo $audit['store_name']; ?></div>
    </div>

    <div class="report-info">
        <div class="row">
            <div class="col-md-6">
                <div class="report-info-item"><strong>Yoxlama ID:</strong> <?php echo $audit['id']; ?></div>
                <div class="report-info-item"><strong>Mağaza:</strong> <?php echo $audit['store_name']; ?></div>
                <div class="report-info-item"><strong>Şablon:</strong> <?php echo $audit['template_name']; ?></div>
            </div>
            <div class="col-md-6">
                <div class="report-info-item"><strong>Auditor:</strong> <?php echo $audit['auditor']; ?></div>
                <div class="report-info-item"><strong>Yaradılma tarixi:</strong> <?php echo date('d.m.Y H:i', strtotime($audit['created_at'])); ?></div>
                <div class="report-info-item"><strong>Tamamlanma tarixi:</strong> <?php echo date('d.m.Y H:i', strtotime($audit['completed_at'])); ?></div>
                <div class="report-info-item"><strong>Ümumi xal:</strong> <?php echo number_format($audit['total_score'], 2); ?></div>
            </div>
        </div>
    </div>

    <?php foreach ($audit['template']['sections'] as $section): ?>
    <div class="section-name-header mb-2">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><?php echo $section['name']; ?></h5>
            <?php if (isset($audit['section_scores'])): ?>
                <?php
                $sectionScores = json_decode($audit['section_scores'], true);
                if (isset($sectionScores[$section['id']]) && $sectionScores[$section['id']]['average'] !== null):
                    $avgScore = round($sectionScores[$section['id']]['average'], 2);
                    $scoreClass = $avgScore >= 7 ? 'success' : ($avgScore >= 5 ? 'warning' : 'danger');
                ?>
                <span class="badge bg-<?php echo $scoreClass; ?> section-score">
                    Ortalama xal: <?php echo $avgScore; ?>
                </span>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th width="30%">Element</th>
                <th width="25%">Status</th>
                <th width="25%">Qeyd</th>
                <th width="20%">Xal</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($section['items'] as $item): ?>
            <tr>
                <td><strong><?php echo $item['name']; ?></strong></td>
                <td>
                    <?php
                    if (isset($audit['items'][$item['id']])) {
                        switch ($audit['items'][$item['id']]['status']) {
                            case 'yes':
                                echo 'Bəli';
                                break;
                            case 'no':
                                echo 'Xeyr';
                                break;
                            default:
                                echo 'Yoxlanılmayıb';
                        }
                    } else {
                        echo 'Yoxlanılmayıb';
                    }
                    ?>
                </td>
                <td>
                    <?php echo isset($audit['items'][$item['id']]) && !empty($audit['items'][$item['id']]['notes']) ? $audit['items'][$item['id']]['notes'] : '-'; ?>
                </td>
                <td>
                    <?php if (isset($item['has_score']) && $item['has_score']): ?>
                        <?php echo isset($audit['items'][$item['id']]) && $audit['items'][$item['id']]['score'] !== null ? $audit['items'][$item['id']]['score'] : '-'; ?>
                    <?php else: ?>
                        -
                    <?php endif; ?>
                </td>
            </tr>
            <?php if (isset($audit['items'][$item['id']]) && !empty($audit['items'][$item['id']]['images'])): ?>
            <tr>
                <td colspan="4">
                    <div>
                        <strong>Şəkillər:</strong>
                        <div class="row mt-2">
                            <?php foreach ($audit['items'][$item['id']]['images'] as $image): ?>
                            <div class="col-md-4 col-sm-6 mb-2">
                                <img src="<?php echo $upload_dir . $image['image']; ?>" alt="Image" class="item-image">
                                <div class="text-center">
                                    <small><?php echo date('d.m.Y', filemtime(getImageFullPath($image['image']))); ?></small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </td>
            </tr>
            <?php endif; ?>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php endforeach; ?>

    <?php if (!empty($audit['general_notes'])): ?>
    <div class="mt-4">
        <h4>Ümumi qeydlər</h4>
        <div class="p-3 border rounded">
            <?php echo nl2br(htmlspecialchars($audit['general_notes'])); ?>
        </div>
    </div>
    <?php endif; ?>

    <div class="report-footer">
        <p>Bu hesabat <?php echo date('d.m.Y H:i'); ?> tarixində yaradılmışdır.</p>
        <p>Audit System</p>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
