<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('view_audits');

// Get template ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış şablon ID', 'alert alert-danger');
    redirect('templates.php');
}

// Get template data
$template = getTemplateById($id);

if (!$template) {
    flash('error_message', 'Şablon tapılmadı', 'alert alert-danger');
    redirect('templates.php');
}

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Şablon: <?php echo $template['name']; ?></h1>
    
    <div>
        <a href="templates.php" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left"></i> Geri
        </a>
        <a href="template_form.php?id=<?php echo $id; ?>" class="btn btn-primary">
            <i class="fas fa-edit"></i> Redaktə et
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Şablon məlumatları</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Ad:</strong> <?php echo $template['name']; ?></p>
            </div>
            <div class="col-md-6">
                <p><strong>Yaradılma tarixi:</strong> <?php echo date('d.m.Y H:i', strtotime($template['created_at'])); ?></p>
            </div>
        </div>
    </div>
</div>

<?php if (empty($template['sections'])): ?>
<div class="alert alert-info">
    Bu şablonda heç bir bölmə yoxdur.
</div>
<?php else: ?>
    <?php foreach ($template['sections'] as $section): ?>
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><?php echo $section['name']; ?></h5>
        </div>
        <div class="card-body">
            <?php if (empty($section['items'])): ?>
            <div class="alert alert-info">
                Bu bölmədə heç bir element yoxdur.
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th>Element adı</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($section['items'] as $index => $item): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo $item['name']; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endforeach; ?>
<?php endif; ?>

<?php include 'footer.php'; ?>
