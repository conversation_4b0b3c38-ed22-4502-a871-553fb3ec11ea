<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('view_completed');

// Get filters
$filters = [];
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;

if (isset($_GET['store_id']) && !empty($_GET['store_id'])) {
    $filters['store_id'] = (int)$_GET['store_id'];
}

if (isset($_GET['min_score']) && !empty($_GET['min_score'])) {
    $filters['min_score'] = (float)$_GET['min_score'];
}

if (isset($_GET['max_score']) && !empty($_GET['max_score'])) {
    $filters['max_score'] = (float)$_GET['max_score'];
}

if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
    $filters['start_date'] = $_GET['start_date'];
}

if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
    $filters['end_date'] = $_GET['end_date'];
}

// Calculate offset
$offset = ($page - 1) * $limit;

// Get completed audits
$audits = getCompletedAudits($filters, $limit, $offset);

// Get total count for pagination
$totalAudits = countCompletedAudits($filters);
$totalPages = ceil($totalAudits / $limit);

// Get stores for filter
$stores = getAllStores();

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>Tamamlanmış yoxlamalar</h1>
        <a href="dashboard.php" class="btn btn-sm btn-secondary mt-2">
            <i class="fas fa-arrow-left"></i> Ana səhifəyə qayıt
        </a>
    </div>
</div>

<div class="filters-container mb-4">
    <form action="completed.php" method="get" class="row g-3">
        <div class="col-md-3">
            <label for="store_id" class="form-label">Mağaza</label>
            <select class="form-select" id="store_id" name="store_id">
                <option value="">Bütün mağazalar</option>
                <?php foreach ($stores as $store): ?>
                <option value="<?php echo $store['id']; ?>" <?php echo isset($filters['store_id']) && $filters['store_id'] == $store['id'] ? 'selected' : ''; ?>>
                    <?php echo $store['name']; ?>
                </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="col-md-2">
            <label for="min_score" class="form-label">Min. xal</label>
            <input type="number" class="form-control" id="min_score" name="min_score" min="0" max="10" step="0.1" value="<?php echo isset($filters['min_score']) ? $filters['min_score'] : ''; ?>">
        </div>

        <div class="col-md-2">
            <label for="max_score" class="form-label">Max. xal</label>
            <input type="number" class="form-control" id="max_score" name="max_score" min="0" max="10" step="0.1" value="<?php echo isset($filters['max_score']) ? $filters['max_score'] : ''; ?>">
        </div>

        <div class="col-md-2">
            <label for="start_date" class="form-label">Başlanğıc tarix</label>
            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo isset($filters['start_date']) ? $filters['start_date'] : ''; ?>">
        </div>

        <div class="col-md-2">
            <label for="end_date" class="form-label">Son tarix</label>
            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo isset($filters['end_date']) ? $filters['end_date'] : ''; ?>">
        </div>

        <div class="col-md-1">
            <label for="limit" class="form-label">Limit</label>
            <select class="form-select" id="limit" name="limit">
                <option value="10" <?php echo $limit == 10 ? 'selected' : ''; ?>>10</option>
                <option value="20" <?php echo $limit == 20 ? 'selected' : ''; ?>>20</option>
                <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50</option>
                <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100</option>
            </select>
        </div>

        <div class="col-12">
            <button type="submit" class="btn btn-primary">Filter</button>
            <a href="completed.php" class="btn btn-secondary">Sıfırla</a>
        </div>
    </form>
</div>

<div class="table-container">
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Mağaza</th>
                    <th>Auditor</th>
                    <th>Xal</th>
                    <th>Tamamlanma tarixi</th>
                    <th>Əməliyyatlar</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($audits)): ?>
                <tr>
                    <td colspan="6" class="text-center">Tamamlanmış yoxlama tapılmadı</td>
                </tr>
                <?php else: ?>
                    <?php foreach ($audits as $audit): ?>
                    <tr>
                        <td><?php echo $audit['id']; ?></td>
                        <td><?php echo $audit['store_name']; ?></td>
                        <td><?php echo $audit['auditor']; ?></td>
                        <td>
                            <span class="badge bg-<?php echo $audit['total_score'] >= 7 ? 'success' : ($audit['total_score'] >= 5 ? 'warning' : 'danger'); ?>">
                                <?php echo number_format($audit['total_score'], 2); ?>
                            </span>
                        </td>
                        <td><?php echo date('d.m.Y H:i', strtotime($audit['completed_at'])); ?></td>
                        <td class="table-actions">
                            <a href="audit_view.php?id=<?php echo $audit['id']; ?>" class="btn btn-sm btn-info btn-action" data-bs-toggle="tooltip" title="<?php echo $_SESSION['user_role'] === 'Admin' ? 'Bax/Redaktə et' : 'Bax'; ?>">
                                <i class="fas fa-<?php echo $_SESSION['user_role'] === 'Admin' ? 'edit' : 'eye'; ?>"></i>
                            </a>
                            <a href="audit_print.php?id=<?php echo $audit['id']; ?>" class="btn btn-sm btn-secondary btn-action" data-bs-toggle="tooltip" title="Çap et">
                                <i class="fas fa-print"></i>
                            </a>
                            <a href="audit_export.php?id=<?php echo $audit['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Yüklə">
                                <i class="fas fa-download"></i>
                            </a>
                            <?php if ($_SESSION['user_role'] === 'Admin'): ?>
                            <a href="delete_audit.php?id=<?php echo $audit['id']; ?>" class="btn btn-sm btn-danger btn-action btn-delete" data-bs-toggle="tooltip" title="Sil">
                                <i class="fas fa-trash"></i>
                            </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <?php if ($totalPages > 1): ?>
    <div class="pagination-container">
        <nav aria-label="Page navigation">
            <ul class="pagination">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="completed.php?page=1<?php echo isset($_GET['limit']) ? '&limit=' . $limit : ''; ?><?php echo isset($filters['store_id']) ? '&store_id=' . $filters['store_id'] : ''; ?><?php echo isset($filters['min_score']) ? '&min_score=' . $filters['min_score'] : ''; ?><?php echo isset($filters['max_score']) ? '&max_score=' . $filters['max_score'] : ''; ?><?php echo isset($filters['start_date']) ? '&start_date=' . $filters['start_date'] : ''; ?><?php echo isset($filters['end_date']) ? '&end_date=' . $filters['end_date'] : ''; ?>" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="completed.php?page=<?php echo $page - 1; ?><?php echo isset($_GET['limit']) ? '&limit=' . $limit : ''; ?><?php echo isset($filters['store_id']) ? '&store_id=' . $filters['store_id'] : ''; ?><?php echo isset($filters['min_score']) ? '&min_score=' . $filters['min_score'] : ''; ?><?php echo isset($filters['max_score']) ? '&max_score=' . $filters['max_score'] : ''; ?><?php echo isset($filters['start_date']) ? '&start_date=' . $filters['start_date'] : ''; ?><?php echo isset($filters['end_date']) ? '&end_date=' . $filters['end_date'] : ''; ?>" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                <?php endif; ?>

                <?php
                $startPage = max(1, $page - 2);
                $endPage = min($totalPages, $page + 2);

                for ($i = $startPage; $i <= $endPage; $i++):
                ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="completed.php?page=<?php echo $i; ?><?php echo isset($_GET['limit']) ? '&limit=' . $limit : ''; ?><?php echo isset($filters['store_id']) ? '&store_id=' . $filters['store_id'] : ''; ?><?php echo isset($filters['min_score']) ? '&min_score=' . $filters['min_score'] : ''; ?><?php echo isset($filters['max_score']) ? '&max_score=' . $filters['max_score'] : ''; ?><?php echo isset($filters['start_date']) ? '&start_date=' . $filters['start_date'] : ''; ?><?php echo isset($filters['end_date']) ? '&end_date=' . $filters['end_date'] : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>

                <?php if ($page < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="completed.php?page=<?php echo $page + 1; ?><?php echo isset($_GET['limit']) ? '&limit=' . $limit : ''; ?><?php echo isset($filters['store_id']) ? '&store_id=' . $filters['store_id'] : ''; ?><?php echo isset($filters['min_score']) ? '&min_score=' . $filters['min_score'] : ''; ?><?php echo isset($filters['max_score']) ? '&max_score=' . $filters['max_score'] : ''; ?><?php echo isset($filters['start_date']) ? '&start_date=' . $filters['start_date'] : ''; ?><?php echo isset($filters['end_date']) ? '&end_date=' . $filters['end_date'] : ''; ?>" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="completed.php?page=<?php echo $totalPages; ?><?php echo isset($_GET['limit']) ? '&limit=' . $limit : ''; ?><?php echo isset($filters['store_id']) ? '&store_id=' . $filters['store_id'] : ''; ?><?php echo isset($filters['min_score']) ? '&min_score=' . $filters['min_score'] : ''; ?><?php echo isset($filters['max_score']) ? '&max_score=' . $filters['max_score'] : ''; ?><?php echo isset($filters['start_date']) ? '&start_date=' . $filters['start_date'] : ''; ?><?php echo isset($filters['end_date']) ? '&end_date=' . $filters['end_date'] : ''; ?>" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<?php include 'footer.php'; ?>
