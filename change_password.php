<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login
requireLogin();

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $currentPassword = $_POST['current_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];
    
    // Validate input
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        flash('error_message', 'Bütün sahələri doldurun', 'alert alert-danger');
        redirect('settings.php');
    }
    
    // Check if new password and confirm password match
    if ($newPassword !== $confirmPassword) {
        flash('error_message', 'Yeni şifrə və təsdiq şifrəsi uyğun gəlmir', 'alert alert-danger');
        redirect('settings.php');
    }
    
    // Get user data
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = :id");
    $stmt->execute(['id' => $_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    // Verify current password
    if (!$user || !password_verify($currentPassword, $user['password'])) {
        flash('error_message', 'Cari şifrə yanlışdır', 'alert alert-danger');
        redirect('settings.php');
    }
    
    // Update password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE id = :id");
    $stmt->execute([
        'password' => $hashedPassword,
        'id' => $_SESSION['user_id']
    ]);
    
    flash('success_message', 'Şifrə uğurla dəyişdirildi');
    redirect('settings.php');
} else {
    // Redirect to settings page if accessed directly
    redirect('settings.php');
}
