<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and admin role
requireLogin();

if ($_SESSION['user_role'] !== 'Admin') {
    flash('error_message', 'Bu səhifəyə giriş icazəniz yoxdur', 'alert alert-danger');
    redirect('dashboard.php');
}

// Get user ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış istifadəçi ID', 'alert alert-danger');
    redirect('settings.php');
}

// Check if user exists
$user = getUserById($id);

if (!$user) {
    flash('error_message', 'İstifadəçi tapılmadı', 'alert alert-danger');
    redirect('settings.php');
}

// Prevent deleting yourself
if ($id == $_SESSION['user_id']) {
    flash('error_message', 'Özünüzü silə bilməzsiniz', 'alert alert-danger');
    redirect('settings.php');
}

// Check if user has audits
global $pdo;
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM audits WHERE user_id = :user_id");
$stmt->execute(['user_id' => $id]);
$result = $stmt->fetch();

if ($result['count'] > 0) {
    flash('error_message', 'Bu istifadəçi yoxlamalarda istifadə olunur və silinə bilməz', 'alert alert-danger');
    redirect('settings.php');
}

// Delete user
if (deleteUser($id)) {
    flash('success_message', 'İstifadəçi uğurla silindi');
} else {
    flash('error_message', 'İstifadəçi silinərkən xəta baş verdi', 'alert alert-danger');
}

redirect('settings.php');
