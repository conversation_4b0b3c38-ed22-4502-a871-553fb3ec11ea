<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('create_audit');

// Initialize variables
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$template = [
    'name' => '',
    'sections' => []
];
$isEdit = false;

// If editing, get template data
if ($id > 0) {
    $template = getTemplateById($id);

    if (!$template) {
        flash('error_message', 'Şablon tapılmadı', 'alert alert-danger');
        redirect('templates.php');
    }

    $isEdit = true;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);

    // Validate input
    if (empty($name)) {
        flash('error_message', 'Şablon adı tələb olunur', 'alert alert-danger');
    } else {
        global $pdo;

        try {
            // Start transaction
            $pdo->beginTransaction();

            if ($isEdit) {
                // Update template
                $stmt = $pdo->prepare("UPDATE templates SET name = :name WHERE id = :id");
                $stmt->execute([
                    'name' => $name,
                    'id' => $id
                ]);
            } else {
                // Create new template
                $stmt = $pdo->prepare("INSERT INTO templates (name) VALUES (:name)");
                $stmt->execute(['name' => $name]);

                $id = $pdo->lastInsertId();
            }

            // Process sections
            if (isset($_POST['section_name']) && is_array($_POST['section_name'])) {
                // If editing, delete existing sections and items
                if ($isEdit) {
                    // Delete sections (cascade will delete items)
                    $stmt = $pdo->prepare("DELETE FROM template_sections WHERE template_id = :template_id");
                    $stmt->execute(['template_id' => $id]);
                }

                // Add new sections and items
                foreach ($_POST['section_name'] as $sectionIndex => $sectionName) {
                    if (!empty($sectionName)) {
                        // Insert section
                        $stmt = $pdo->prepare("INSERT INTO template_sections (template_id, name, order_num) VALUES (:template_id, :name, :order_num)");
                        $stmt->execute([
                            'template_id' => $id,
                            'name' => sanitize($sectionName),
                            'order_num' => $sectionIndex + 1
                        ]);

                        $sectionId = $pdo->lastInsertId();

                        // Insert items for this section
                        if (isset($_POST['item_name'][$sectionIndex]) && is_array($_POST['item_name'][$sectionIndex])) {
                            foreach ($_POST['item_name'][$sectionIndex] as $itemIndex => $itemName) {
                                if (!empty($itemName)) {
                                    $hasScore = isset($_POST['item_has_score'][$sectionIndex][$itemIndex]) ? 1 : 0;
                                    $stmt = $pdo->prepare("INSERT INTO template_items (section_id, name, order_num, has_score) VALUES (:section_id, :name, :order_num, :has_score)");
                                    $stmt->execute([
                                        'section_id' => $sectionId,
                                        'name' => sanitize($itemName),
                                        'order_num' => $itemIndex + 1,
                                        'has_score' => $hasScore
                                    ]);
                                }
                            }
                        }
                    }
                }
            }

            // Commit transaction
            $pdo->commit();

            flash('success_message', $isEdit ? 'Şablon uğurla yeniləndi' : 'Şablon uğurla yaradıldı');
            redirect('templates.php');
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
        }
    }
}

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><?php echo $isEdit ? 'Şablonu redaktə et' : 'Yeni şablon'; ?></h1>

    <a href="templates.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Geri
    </a>
</div>

<div class="form-container">
    <form action="template_form.php<?php echo $isEdit ? '?id=' . $id : ''; ?>" method="post" id="template-form">
        <div class="mb-3">
            <label for="name" class="form-label">Şablon adı *</label>
            <input type="text" class="form-control" id="name" name="name" value="<?php echo $template['name']; ?>" required>
        </div>

        <h4 class="mt-4 mb-3">Bölmələr və elementlər</h4>

        <div id="sections-container">
            <?php if ($isEdit && !empty($template['sections'])): ?>
                <?php foreach ($template['sections'] as $sectionIndex => $section): ?>
                <div class="section-item card mb-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Bölmə</h5>
                            <button type="button" class="btn btn-sm btn-danger remove-section">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Bölmə adı *</label>
                            <input type="text" class="form-control" name="section_name[<?php echo $sectionIndex; ?>]" value="<?php echo $section['name']; ?>" required>
                        </div>

                        <h6 class="mt-3 mb-2">Elementlər</h6>

                        <div class="items-container">
                            <?php foreach ($section['items'] as $itemIndex => $item): ?>
                            <div class="item-row mb-2 d-flex align-items-center">
                                <input type="text" class="form-control me-2" name="item_name[<?php echo $sectionIndex; ?>][<?php echo $itemIndex; ?>]" value="<?php echo $item['name']; ?>" placeholder="Element adı" required>
                                <div class="form-check me-2">
                                    <input class="form-check-input" type="checkbox" name="item_has_score[<?php echo $sectionIndex; ?>][<?php echo $itemIndex; ?>]" id="has_score_<?php echo $sectionIndex; ?>_<?php echo $itemIndex; ?>" <?php echo isset($item['has_score']) && $item['has_score'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="has_score_<?php echo $sectionIndex; ?>_<?php echo $itemIndex; ?>">
                                        Xal
                                    </label>
                                </div>
                                <button type="button" class="btn btn-sm btn-danger remove-item">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <button type="button" class="btn btn-sm btn-success mt-2 add-item">
                            <i class="fas fa-plus"></i> Element əlavə et
                        </button>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="section-item card mb-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Bölmə</h5>
                            <button type="button" class="btn btn-sm btn-danger remove-section">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Bölmə adı *</label>
                            <input type="text" class="form-control" name="section_name[0]" required>
                        </div>

                        <h6 class="mt-3 mb-2">Elementlər</h6>

                        <div class="items-container">
                            <div class="item-row mb-2 d-flex align-items-center">
                                <input type="text" class="form-control me-2" name="item_name[0][0]" placeholder="Element adı" required>
                                <div class="form-check me-2">
                                    <input class="form-check-input" type="checkbox" name="item_has_score[0][0]" id="has_score_0_0" checked>
                                    <label class="form-check-label" for="has_score_0_0">
                                        Xal
                                    </label>
                                </div>
                                <button type="button" class="btn btn-sm btn-danger remove-item">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <button type="button" class="btn btn-sm btn-success mt-2 add-item">
                            <i class="fas fa-plus"></i> Element əlavə et
                        </button>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <button type="button" class="btn btn-info mb-4" id="add-section">
            <i class="fas fa-plus"></i> Bölmə əlavə et
        </button>

        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <a href="templates.php" class="btn btn-secondary me-md-2">Ləğv et</a>
            <button type="submit" class="btn btn-primary">Yadda saxla</button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add section
    document.getElementById('add-section').addEventListener('click', function() {
        const sectionsContainer = document.getElementById('sections-container');
        const sectionCount = sectionsContainer.querySelectorAll('.section-item').length;

        const sectionHtml = `
            <div class="section-item card mb-3">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Bölmə</h5>
                        <button type="button" class="btn btn-sm btn-danger remove-section">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Bölmə adı *</label>
                        <input type="text" class="form-control" name="section_name[${sectionCount}]" required>
                    </div>

                    <h6 class="mt-3 mb-2">Elementlər</h6>

                    <div class="items-container">
                        <div class="item-row mb-2 d-flex align-items-center">
                            <input type="text" class="form-control me-2" name="item_name[${sectionCount}][0]" placeholder="Element adı" required>
                            <div class="form-check me-2">
                                <input class="form-check-input" type="checkbox" name="item_has_score[${sectionCount}][0]" id="has_score_${sectionCount}_0" checked>
                                <label class="form-check-label" for="has_score_${sectionCount}_0">
                                    Xal
                                </label>
                            </div>
                            <button type="button" class="btn btn-sm btn-danger remove-item">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <button type="button" class="btn btn-sm btn-success mt-2 add-item">
                        <i class="fas fa-plus"></i> Element əlavə et
                    </button>
                </div>
            </div>
        `;

        sectionsContainer.insertAdjacentHTML('beforeend', sectionHtml);
        initEvents();
    });

    // Initialize events for dynamic elements
    function initEvents() {
        // Remove section
        document.querySelectorAll('.remove-section').forEach(button => {
            button.addEventListener('click', function() {
                if (document.querySelectorAll('.section-item').length > 1 || confirm('Bütün bölmələri silmək istədiyinizə əminsiniz?')) {
                    this.closest('.section-item').remove();
                    reindexSections();
                }
            });
        });

        // Add item
        document.querySelectorAll('.add-item').forEach(button => {
            button.addEventListener('click', function() {
                const itemsContainer = this.previousElementSibling;
                const sectionIndex = this.closest('.section-item').querySelector('input[name^="section_name"]').name.match(/\d+/)[0];
                const itemCount = itemsContainer.querySelectorAll('.item-row').length;

                const itemHtml = `
                    <div class="item-row mb-2 d-flex align-items-center">
                        <input type="text" class="form-control me-2" name="item_name[${sectionIndex}][${itemCount}]" placeholder="Element adı" required>
                        <div class="form-check me-2">
                            <input class="form-check-input" type="checkbox" name="item_has_score[${sectionIndex}][${itemCount}]" id="has_score_${sectionIndex}_${itemCount}" checked>
                            <label class="form-check-label" for="has_score_${sectionIndex}_${itemCount}">
                                Xal
                            </label>
                        </div>
                        <button type="button" class="btn btn-sm btn-danger remove-item">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                itemsContainer.insertAdjacentHTML('beforeend', itemHtml);
                initEvents();
            });
        });

        // Remove item
        document.querySelectorAll('.remove-item').forEach(button => {
            button.addEventListener('click', function() {
                const itemsContainer = this.closest('.items-container');

                if (itemsContainer.querySelectorAll('.item-row').length > 1 || confirm('Bütün elementləri silmək istədiyinizə əminsiniz?')) {
                    this.closest('.item-row').remove();
                    reindexItems(itemsContainer);
                }
            });
        });
    }

    // Reindex sections after removal
    function reindexSections() {
        const sections = document.querySelectorAll('.section-item');

        sections.forEach((section, sectionIndex) => {
            const sectionNameInput = section.querySelector('input[name^="section_name"]');
            sectionNameInput.name = `section_name[${sectionIndex}]`;

            reindexItems(section.querySelector('.items-container'), sectionIndex);
        });
    }

    // Reindex items after removal
    function reindexItems(itemsContainer, sectionIndex) {
        const items = itemsContainer.querySelectorAll('.item-row');

        if (sectionIndex === undefined) {
            sectionIndex = itemsContainer.closest('.section-item').querySelector('input[name^="section_name"]').name.match(/\d+/)[0];
        }

        items.forEach((item, itemIndex) => {
            const itemNameInput = item.querySelector('input[name^="item_name"]');
            itemNameInput.name = `item_name[${sectionIndex}][${itemIndex}]`;
        });
    }

    // Form validation
    document.getElementById('template-form').addEventListener('submit', function(e) {
        const sections = document.querySelectorAll('.section-item');
        let valid = true;

        if (sections.length === 0) {
            alert('Ən azı bir bölmə əlavə edin');
            valid = false;
        } else {
            sections.forEach(section => {
                const sectionName = section.querySelector('input[name^="section_name"]').value.trim();
                const items = section.querySelectorAll('.item-row');

                if (sectionName === '') {
                    alert('Bütün bölmə adlarını doldurun');
                    valid = false;
                }

                if (items.length === 0) {
                    alert('Hər bölməyə ən azı bir element əlavə edin');
                    valid = false;
                } else {
                    items.forEach(item => {
                        const itemName = item.querySelector('input[name^="item_name"]').value.trim();

                        if (itemName === '') {
                            alert('Bütün element adlarını doldurun');
                            valid = false;
                        }
                    });
                }
            });
        }

        if (!valid) {
            e.preventDefault();
        }
    });

    // Initialize events on page load
    initEvents();
});
</script>

<?php include 'footer.php'; ?>
