<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login
requireLogin();

// Get image ID and audit ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$auditId = isset($_GET['audit_id']) ? (int)$_GET['audit_id'] : 0;

if ($id <= 0 || $auditId <= 0) {
    flash('error_message', 'Yanlış şəkil ID və ya yoxlama ID', 'alert alert-danger');
    redirect('audits.php');
}

// Get audit data
$audit = getAuditById($auditId);

if (!$audit) {
    flash('error_message', 'Yoxlama tapılmadı', 'alert alert-danger');
    redirect('audits.php');
}

// Check if user has permission to edit this audit
if (!hasPermission('edit_audit') && $audit['user_id'] != $_SESSION['user_id']) {
    flash('error_message', 'Bu yoxlamanı redaktə etmək üçün icazəniz yoxdur', 'alert alert-danger');
    redirect('audits.php');
}

// Get image data
global $pdo;
$stmt = $pdo->prepare("SELECT * FROM audit_item_images WHERE id = :id");
$stmt->execute(['id' => $id]);
$image = $stmt->fetch();

if (!$image) {
    flash('error_message', 'Şəkil tapılmadı', 'alert alert-danger');
    redirect('audit_view.php?id=' . $auditId);
}

// Delete image file
deleteImage($image['image']);

// Delete image from database
$stmt = $pdo->prepare("DELETE FROM audit_item_images WHERE id = :id");
$stmt->execute(['id' => $id]);

flash('success_message', 'Şəkil uğurla silindi');
redirect('audit_view.php?id=' . $auditId);
