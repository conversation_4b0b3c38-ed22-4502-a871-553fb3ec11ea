// Sidebar JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Sidebar toggle
    const sidebarCollapse = document.getElementById('sidebarCollapse');
    const sidebar = document.getElementById('sidebar');
    const content = document.getElementById('content');
    
    if (sidebarCollapse) {
        sidebarCollapse.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            content.classList.toggle('active');
        });
    }
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        const isClickInsideSidebar = sidebar.contains(event.target);
        const isClickOnToggleButton = sidebarCollapse.contains(event.target);
        
        if (!isClickInsideSidebar && !isClickOnToggleButton && window.innerWidth < 768 && !sidebar.classList.contains('active')) {
            sidebar.classList.add('active');
            content.classList.add('active');
        }
    });
    
    // Close sidebar when clicking on a menu item on mobile
    const sidebarLinks = document.querySelectorAll('#sidebar a');
    
    sidebarLinks.forEach(function(link) {
        link.addEventListener('click', function() {
            if (window.innerWidth < 768) {
                sidebar.classList.add('active');
                content.classList.add('active');
            }
        });
    });
    
    // Add overlay for mobile
    const body = document.querySelector('body');
    const overlay = document.createElement('div');
    overlay.classList.add('overlay');
    body.appendChild(overlay);
    
    // Toggle overlay with sidebar
    sidebarCollapse.addEventListener('click', function() {
        overlay.classList.toggle('active');
    });
    
    // Close sidebar when clicking on overlay
    overlay.addEventListener('click', function() {
        sidebar.classList.add('active');
        content.classList.add('active');
        overlay.classList.remove('active');
    });
});
