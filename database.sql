-- Create database if not exists
-- Database is already created on the server: brendh5_checkup

-- Users table
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Roles table
CREATE TABLE IF NOT EXISTS `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `permissions` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Stores table
CREATE TABLE IF NOT EXISTS `stores` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Templates table
CREATE TABLE IF NOT EXISTS `templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Template sections table
CREATE TABLE IF NOT EXISTS `template_sections` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `order_num` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `template_id` (`template_id`),
  CONSTRAINT `template_sections_ibfk_1` FOREIGN KEY (`template_id`) REFERENCES `templates` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Template items table
CREATE TABLE IF NOT EXISTS `template_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `section_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `order_num` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `section_id` (`section_id`),
  CONSTRAINT `template_items_ibfk_1` FOREIGN KEY (`section_id`) REFERENCES `template_sections` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Audits table
CREATE TABLE IF NOT EXISTS `audits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL,
  `template_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `status` enum('in_progress','completed') NOT NULL DEFAULT 'in_progress',
  `total_score` decimal(5,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `store_id` (`store_id`),
  KEY `template_id` (`template_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `audits_ibfk_1` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`),
  CONSTRAINT `audits_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `templates` (`id`),
  CONSTRAINT `audits_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Audit items table
CREATE TABLE IF NOT EXISTS `audit_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `audit_id` int(11) NOT NULL,
  `template_item_id` int(11) NOT NULL,
  `status` enum('yes','no','not_checked') DEFAULT 'not_checked',
  `score` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `audit_id` (`audit_id`),
  KEY `template_item_id` (`template_item_id`),
  CONSTRAINT `audit_items_ibfk_1` FOREIGN KEY (`audit_id`) REFERENCES `audits` (`id`) ON DELETE CASCADE,
  CONSTRAINT `audit_items_ibfk_2` FOREIGN KEY (`template_item_id`) REFERENCES `template_items` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Visit plan table
CREATE TABLE IF NOT EXISTS `visit_plans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `visit_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `visit_plans_ibfk_1` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`),
  CONSTRAINT `visit_plans_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default roles
INSERT INTO `roles` (`name`, `permissions`) VALUES
('Admin', '{"all": true}'),
('Audit', '{"view_stores": true, "view_audits": true, "create_audit": true, "edit_audit": true, "view_completed": true, "manage_visit_plan": true}'),
('Menecer', '{"view_stores": true, "view_audits": true, "view_completed": true, "manage_visit_plan": true}');

-- Insert default users
INSERT INTO `users` (`username`, `password`, `role_id`) VALUES
('Admin', '$2y$10$Oa0ESzUkZV0uwVm1JZr.UOW.ZyKVK2jVVL7JLYCHa9ZyAJZ9D1iMi', 1), -- Admin123
('Audit', '$2y$10$Oa0ESzUkZV0uwVm1JZr.UOW.ZyKVK2jVVL7JLYCHa9ZyAJZ9D1iMi', 2), -- Audit123
('Menecer', '$2y$10$Oa0ESzUkZV0uwVm1JZr.UOW.ZyKVK2jVVL7JLYCHa9ZyAJZ9D1iMi', 3); -- Menecer123

-- Insert sample stores
INSERT INTO `stores` (`name`, `address`) VALUES
('Mağaza 1', 'Ünvan 1'),
('Mağaza 2', 'Ünvan 2');

-- Insert sample template
INSERT INTO `templates` (`name`) VALUES
('Standart Yoxlama');

-- Insert sample template sections
INSERT INTO `template_sections` (`template_id`, `name`, `order_num`) VALUES
(1, 'Çöl görünüş', 1);

-- Insert sample template items
INSERT INTO `template_items` (`section_id`, `name`, `order_num`) VALUES
(1, 'Mağaza ətrafının təmizliyi', 1),
(1, 'Vitrin şüşələrinin təmizliyi', 2),
(1, 'Dekorasiyanın səliqəliliyi', 3),
(1, 'Aktual reklam, post materialların asılması', 4),
(1, 'Giriş zona', 5);
