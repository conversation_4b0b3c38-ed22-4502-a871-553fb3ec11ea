<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('delete_store');

// Get store ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış mağaza ID', 'alert alert-danger');
    redirect('stores.php');
}

// Check if store exists
$store = getStoreById($id);

if (!$store) {
    flash('error_message', 'Mağaza tapılmadı', 'alert alert-danger');
    redirect('stores.php');
}

// Check if store has audits
global $pdo;
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM audits WHERE store_id = :store_id");
$stmt->execute(['store_id' => $id]);
$result = $stmt->fetch();

if ($result['count'] > 0) {
    flash('error_message', 'Bu mağaza yoxlamalarda istifadə olunur və silinə bilməz', 'alert alert-danger');
    redirect('stores.php');
}

// Delete store
try {
    $stmt = $pdo->prepare("DELETE FROM stores WHERE id = :id");
    $stmt->execute(['id' => $id]);
    
    flash('success_message', 'Mağaza uğurla silindi');
} catch (PDOException $e) {
    flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
}

redirect('stores.php');
