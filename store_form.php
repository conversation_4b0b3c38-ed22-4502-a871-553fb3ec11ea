<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('create_store');

// Initialize variables
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$store = [
    'name' => '',
    'store_code' => '',
    'address' => '',
    'phone' => '',
    'email' => '',
    'manager' => '',
    'notes' => ''
];
$isEdit = false;

// If editing, get store data
if ($id > 0) {
    requirePermission('edit_store');
    $store = getStoreById($id);

    if (!$store) {
        flash('error_message', 'Mağaza tapılmadı', 'alert alert-danger');
        redirect('stores.php');
    }

    $isEdit = true;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $store_code = sanitize($_POST['store_code']);
    $address = sanitize($_POST['address']);
    $phone = sanitize($_POST['phone']);
    $email = sanitize($_POST['email']);
    $manager = sanitize($_POST['manager']);
    $notes = sanitize($_POST['notes']);

    // Validate input
    if (empty($name)) {
        flash('error_message', 'Mağaza adı tələb olunur', 'alert alert-danger');
    } else {
        global $pdo;

        try {
            if ($isEdit) {
                // Update store
                $stmt = $pdo->prepare("UPDATE stores SET name = :name, store_code = :store_code, address = :address, phone = :phone, email = :email, manager = :manager, notes = :notes WHERE id = :id");
                $stmt->execute([
                    'name' => $name,
                    'store_code' => $store_code,
                    'address' => $address,
                    'phone' => $phone,
                    'email' => $email,
                    'manager' => $manager,
                    'notes' => $notes,
                    'id' => $id
                ]);

                flash('success_message', 'Mağaza uğurla yeniləndi');
            } else {
                // Create new store
                $stmt = $pdo->prepare("INSERT INTO stores (name, store_code, address, phone, email, manager, notes) VALUES (:name, :store_code, :address, :phone, :email, :manager, :notes)");
                $stmt->execute([
                    'name' => $name,
                    'store_code' => $store_code,
                    'address' => $address,
                    'phone' => $phone,
                    'email' => $email,
                    'manager' => $manager,
                    'notes' => $notes
                ]);

                flash('success_message', 'Mağaza uğurla yaradıldı');
            }

            redirect('stores.php');
        } catch (PDOException $e) {
            flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
        }
    }
}

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><?php echo $isEdit ? 'Mağazanı redaktə et' : 'Yeni mağaza'; ?></h1>

    <a href="stores.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Geri
    </a>
</div>

<div class="form-container">
    <form action="store_form.php<?php echo $isEdit ? '?id=' . $id : ''; ?>" method="post">
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="name" class="form-label">1. Mağaza adı *</label>
                <input type="text" class="form-control" id="name" name="name" value="<?php echo $store['name']; ?>" required>
            </div>

            <div class="col-md-6 mb-3">
                <label for="store_code" class="form-label">2. Mağaza kodu</label>
                <input type="text" class="form-control" id="store_code" name="store_code" value="<?php echo $store['store_code'] ?? ''; ?>">
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 mb-3">
                <label for="address" class="form-label">3. Ünvan</label>
                <input type="text" class="form-control" id="address" name="address" value="<?php echo $store['address'] ?? ''; ?>">
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="phone" class="form-label">4. Telefon</label>
                <input type="text" class="form-control" id="phone" name="phone" value="<?php echo $store['phone'] ?? ''; ?>">
            </div>

            <div class="col-md-6 mb-3">
                <label for="email" class="form-label">5. E-mail</label>
                <input type="email" class="form-control" id="email" name="email" value="<?php echo $store['email'] ?? ''; ?>">
            </div>
        </div>

        <div class="mb-3">
            <label for="manager" class="form-label">6. Mağaza meneceri</label>
            <input type="text" class="form-control" id="manager" name="manager" value="<?php echo $store['manager'] ?? ''; ?>">
        </div>

        <div class="mb-3">
            <label for="notes" class="form-label">7. Qeyd</label>
            <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $store['notes'] ?? ''; ?></textarea>
        </div>

        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <a href="stores.php" class="btn btn-secondary me-md-2">Ləğv et</a>
            <button type="submit" class="btn btn-primary">Yadda saxla</button>
        </div>
    </form>
</div>

<?php include 'footer.php'; ?>
