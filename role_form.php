<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and admin role
requireLogin();

if ($_SESSION['user_role'] !== 'Admin') {
    flash('error_message', 'Bu səhifəyə giriş icazəniz yoxdur', 'alert alert-danger');
    redirect('dashboard.php');
}

// Initialize variables
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$role = [
    'name' => '',
    'permissions' => '{}'
];
$isEdit = false;

// If editing, get role data
if ($id > 0) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM roles WHERE id = :id");
    $stmt->execute(['id' => $id]);
    $role = $stmt->fetch();
    
    if (!$role) {
        flash('error_message', 'Rol tapılmadı', 'alert alert-danger');
        redirect('settings.php');
    }
    
    $isEdit = true;
}

// Define available permissions
$availablePermissions = [
    'view_stores' => 'Mağazaları görmək',
    'create_store' => 'Mağaza yaratmaq',
    'edit_store' => 'Mağazanı redaktə etmək',
    'delete_store' => 'Mağazanı silmək',
    'view_audits' => 'Yoxlamaları görmək',
    'create_audit' => 'Yoxlama yaratmaq',
    'edit_audit' => 'Yoxlamanı redaktə etmək',
    'delete_audit' => 'Yoxlamanı silmək',
    'view_completed' => 'Tamamlanmış yoxlamaları görmək',
    'manage_visit_plan' => 'Ziyarət planını idarə etmək'
];

// Get current permissions
$currentPermissions = json_decode($role['permissions'], true) ?? [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $permissions = [];
    
    // Get selected permissions
    foreach ($availablePermissions as $key => $label) {
        if (isset($_POST['permissions'][$key])) {
            $permissions[$key] = true;
        }
    }
    
    // Validate input
    if (empty($name)) {
        flash('error_message', 'Rol adı tələb olunur', 'alert alert-danger');
    } else {
        global $pdo;
        
        try {
            if ($isEdit) {
                // Update role
                $stmt = $pdo->prepare("UPDATE roles SET name = :name, permissions = :permissions WHERE id = :id");
                $stmt->execute([
                    'name' => $name,
                    'permissions' => json_encode($permissions),
                    'id' => $id
                ]);
                
                flash('success_message', 'Rol uğurla yeniləndi');
            } else {
                // Create new role
                $stmt = $pdo->prepare("INSERT INTO roles (name, permissions) VALUES (:name, :permissions)");
                $stmt->execute([
                    'name' => $name,
                    'permissions' => json_encode($permissions)
                ]);
                
                flash('success_message', 'Rol uğurla yaradıldı');
            }
            
            redirect('settings.php');
        } catch (PDOException $e) {
            flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
        }
    }
}

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><?php echo $isEdit ? 'Rolu redaktə et' : 'Yeni rol'; ?></h1>
    
    <a href="settings.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Geri
    </a>
</div>

<div class="form-container">
    <form action="role_form.php<?php echo $isEdit ? '?id=' . $id : ''; ?>" method="post">
        <div class="mb-3">
            <label for="name" class="form-label">Rol adı *</label>
            <input type="text" class="form-control" id="name" name="name" value="<?php echo $role['name']; ?>" required>
        </div>
        
        <div class="mb-3">
            <label class="form-label">İcazələr</label>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($availablePermissions as $key => $label): ?>
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="permission-<?php echo $key; ?>" name="permissions[<?php echo $key; ?>]" <?php echo isset($currentPermissions[$key]) && $currentPermissions[$key] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="permission-<?php echo $key; ?>">
                                    <?php echo $label; ?>
                                </label>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <a href="settings.php" class="btn btn-secondary me-md-2">Ləğv et</a>
            <button type="submit" class="btn btn-primary">Yadda saxla</button>
        </div>
    </form>
</div>

<?php include 'footer.php'; ?>
