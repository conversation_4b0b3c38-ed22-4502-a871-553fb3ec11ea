<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('create_audit');

// Get template ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış şablon ID', 'alert alert-danger');
    redirect('templates.php');
}

// Check if template exists
global $pdo;
$stmt = $pdo->prepare("SELECT * FROM templates WHERE id = :id");
$stmt->execute(['id' => $id]);
$template = $stmt->fetch();

if (!$template) {
    flash('error_message', 'Şablon tapılmadı', 'alert alert-danger');
    redirect('templates.php');
}

// Check if template is used in audits
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM audits WHERE template_id = :template_id");
$stmt->execute(['template_id' => $id]);
$result = $stmt->fetch();

if ($result['count'] > 0) {
    flash('error_message', 'Bu şablon yoxlamalarda istifadə olunur və silinə bilməz', 'alert alert-danger');
    redirect('templates.php');
}

// Delete template
try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Delete template sections and items (cascade will delete items)
    $stmt = $pdo->prepare("DELETE FROM template_sections WHERE template_id = :template_id");
    $stmt->execute(['template_id' => $id]);
    
    // Delete template
    $stmt = $pdo->prepare("DELETE FROM templates WHERE id = :id");
    $stmt->execute(['id' => $id]);
    
    // Commit transaction
    $pdo->commit();
    
    flash('success_message', 'Şablon uğurla silindi');
} catch (PDOException $e) {
    // Rollback transaction on error
    $pdo->rollBack();
    flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
}

redirect('templates.php');
