<?php
/**
 * Image handling functions
 */

/**
 * Upload and optimize image
 * 
 * @param array $file $_FILES array element
 * @param string $prefix Prefix for the filename
 * @param int $maxWidth Maximum width of the image
 * @param int $maxHeight Maximum height of the image
 * @param int $quality JPEG quality (0-100)
 * @return string|false Filename if successful, false otherwise
 */
function uploadAndOptimizeImage($file, $prefix = '', $maxWidth = 1200, $maxHeight = 1200, $quality = 80) {
    // Check if file is valid
    if (!isset($file['tmp_name']) || empty($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return false;
    }
    
    // Get file info
    $fileInfo = pathinfo($file['name']);
    $extension = strtolower($fileInfo['extension']);
    
    // Check if file is an image
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
    if (!in_array($extension, $allowedExtensions)) {
        return false;
    }
    
    // Create directory structure based on date (year/month/day)
    $dateDir = date('Y/m/d');
    $uploadDir = UPLOAD_PATH . '/' . $dateDir;
    
    // Create directories if they don't exist
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Generate unique filename
    $filename = $prefix . '_' . time() . '_' . mt_rand(1000, 9999) . '.' . $extension;
    $uploadPath = $uploadDir . '/' . $filename;
    
    // Get image info
    list($width, $height, $type) = getimagesize($file['tmp_name']);
    
    // Check if image needs resizing
    $resize = ($width > $maxWidth || $height > $maxHeight);
    
    // Calculate new dimensions while maintaining aspect ratio
    if ($resize) {
        $ratio = min($maxWidth / $width, $maxHeight / $height);
        $newWidth = round($width * $ratio);
        $newHeight = round($height * $ratio);
    } else {
        $newWidth = $width;
        $newHeight = $height;
    }
    
    // Create image resource based on file type
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($file['tmp_name']);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($file['tmp_name']);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($file['tmp_name']);
            break;
        default:
            return false;
    }
    
    // Create new image with new dimensions
    $destination = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($destination, false);
        imagesavealpha($destination, true);
        $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
        imagefilledrectangle($destination, 0, 0, $newWidth, $newHeight, $transparent);
    }
    
    // Resize image
    imagecopyresampled($destination, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
    
    // Save image
    $success = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $success = imagejpeg($destination, $uploadPath, $quality);
            break;
        case IMAGETYPE_PNG:
            // Convert PNG quality (0-100) to PNG compression (0-9)
            $pngQuality = round((100 - $quality) / 11.111111);
            $success = imagepng($destination, $uploadPath, $pngQuality);
            break;
        case IMAGETYPE_GIF:
            $success = imagegif($destination, $uploadPath);
            break;
    }
    
    // Free memory
    imagedestroy($source);
    imagedestroy($destination);
    
    if ($success) {
        // Return relative path to be stored in database
        return $dateDir . '/' . $filename;
    } else {
        return false;
    }
}

/**
 * Get full path of an image
 * 
 * @param string $relativePath Relative path of the image
 * @return string Full path of the image
 */
function getImageFullPath($relativePath) {
    return UPLOAD_PATH . '/' . $relativePath;
}

/**
 * Delete image
 * 
 * @param string $relativePath Relative path of the image
 * @return boolean True if successful, false otherwise
 */
function deleteImage($relativePath) {
    $fullPath = getImageFullPath($relativePath);
    if (file_exists($fullPath)) {
        return unlink($fullPath);
    }
    return false;
}
