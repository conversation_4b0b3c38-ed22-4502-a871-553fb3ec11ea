<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('view_stores');

// Get store ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    flash('error_message', 'Yanlış mağaza ID', 'alert alert-danger');
    redirect('stores.php');
}

// Get store data
global $pdo;
$stmt = $pdo->prepare("SELECT * FROM stores WHERE id = :id");
$stmt->execute(['id' => $id]);
$store = $stmt->fetch();

if (!$store) {
    flash('error_message', 'Mağaza tapılmadı', 'alert alert-danger');
    redirect('stores.php');
}

// Get audits for this store
$stmt = $pdo->prepare("SELECT a.*, t.name as template_name, u.username as auditor
                      FROM audits a
                      JOIN templates t ON a.template_id = t.id
                      JOIN users u ON a.user_id = u.id
                      WHERE a.store_id = :store_id
                      ORDER BY a.created_at DESC");
$stmt->execute(['store_id' => $id]);
$audits = $stmt->fetchAll();

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>Mağaza məlumatları</h1>
        <a href="stores.php" class="btn btn-sm btn-secondary mt-2">
            <i class="fas fa-arrow-left"></i> Mağazalar siyahısına qayıt
        </a>
    </div>

    <div>
        <?php if (hasPermission('create_audit')): ?>
        <a href="audit_form.php?store_id=<?php echo $id; ?>" class="btn btn-success">
            <i class="fas fa-clipboard-check"></i> Yoxlama yarat
        </a>
        <?php endif; ?>

        <?php if (hasPermission('edit_store')): ?>
        <a href="store_form.php?id=<?php echo $id; ?>" class="btn btn-primary ms-2">
            <i class="fas fa-edit"></i> Redaktə et
        </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Mağaza məlumatları</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tr>
                        <th width="30%">Mağaza adı:</th>
                        <td><?php echo $store['name']; ?></td>
                    </tr>
                    <tr>
                        <th>Mağaza kodu:</th>
                        <td><?php echo $store['store_code'] ?? '-'; ?></td>
                    </tr>
                    <tr>
                        <th>Ünvan:</th>
                        <td><?php echo $store['address'] ?? '-'; ?></td>
                    </tr>
                    <tr>
                        <th>Telefon:</th>
                        <td><?php echo $store['phone'] ?? '-'; ?></td>
                    </tr>
                    <tr>
                        <th>E-mail:</th>
                        <td><?php echo $store['email'] ?? '-'; ?></td>
                    </tr>
                    <tr>
                        <th>Mağaza meneceri:</th>
                        <td><?php echo $store['manager'] ?? '-'; ?></td>
                    </tr>
                    <tr>
                        <th>Yaradılma tarixi:</th>
                        <td><?php echo date('d.m.Y H:i', strtotime($store['created_at'])); ?></td>
                    </tr>
                    <?php if (!empty($store['notes'])): ?>
                    <tr>
                        <th>Qeyd:</th>
                        <td><?php echo $store['notes']; ?></td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Son yoxlamalar</h5>
            </div>
            <div class="card-body">
                <?php if (empty($audits)): ?>
                <div class="alert alert-info">
                    Bu mağaza üçün hələ yoxlama aparılmayıb.
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Tarix</th>
                                <th>Şablon</th>
                                <th>Status</th>
                                <th>Xal</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($audits as $audit): ?>
                            <tr>
                                <td><?php echo date('d.m.Y', strtotime($audit['created_at'])); ?></td>
                                <td><?php echo $audit['template_name']; ?></td>
                                <td>
                                    <?php if ($audit['status'] == 'completed'): ?>
                                    <span class="badge bg-success">Tamamlanıb</span>
                                    <?php else: ?>
                                    <span class="badge bg-warning">Davam edir</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($audit['status'] == 'completed'): ?>
                                    <span class="badge bg-<?php echo $audit['total_score'] >= 7 ? 'success' : ($audit['total_score'] >= 5 ? 'warning' : 'danger'); ?>">
                                        <?php echo number_format($audit['total_score'], 2); ?>
                                    </span>
                                    <?php else: ?>
                                    -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="audit_view.php?id=<?php echo $audit['id']; ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'footer.php'; ?>
