<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Check if user is already logged in
if (isLoggedIn()) {
    redirect('dashboard.php');
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    
    // Validate input
    if (empty($username) || empty($password)) {
        flash('login_message', 'Zəhmət olmasa istifadəçi adı və şifrəni daxil edin', 'alert alert-danger');
    } else {
        // Attempt to login
        if (login($username, $password)) {
            redirect('dashboard.php');
        } else {
            flash('login_message', 'Yanlış istifadəçi adı və ya şifrə', 'alert alert-danger');
        }
    }
}

// Include header
include 'header.php';
?>

<div class="login-form">
    <h2>Audit System</h2>
    <form action="index.php" method="post">
        <div class="mb-3">
            <label for="username" class="form-label">İstifadəçi adı</label>
            <input type="text" class="form-control" id="username" name="username" required>
        </div>
        <div class="mb-3">
            <label for="password" class="form-label">Şifrə</label>
            <input type="password" class="form-control" id="password" name="password" required>
        </div>
        <button type="submit" class="btn btn-primary w-100">Daxil ol</button>
    </form>
</div>

<?php include 'footer.php'; ?>
