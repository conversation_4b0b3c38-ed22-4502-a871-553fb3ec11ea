<?php
// Bu skript Audit roluna yoxlamaları görmək icazəsi əlavə edir

require_once 'config.php';
require_once 'functions.php';

// Düzəltmə haqqında məlumat
echo "<h1>Audit rolu yeniləmə skripti</h1>";
echo "<p>Bu skript Audit roluna yoxlamaları görmək icazəsi əlavə edir.</p>";

try {
    global $pdo;
    
    // Audit rolunu əldə et
    $stmt = $pdo->prepare("SELECT * FROM roles WHERE name = 'Audit'");
    $stmt->execute();
    $role = $stmt->fetch();
    
    if ($role) {
        // Mövcud icazələri əldə et
        $permissions = json_decode($role['permissions'], true);
        
        // Yoxlamaları görmək icazəsini əlavə et
        $permissions['view_audits'] = true;
        
        // Rolu yenilə
        $stmt = $pdo->prepare("UPDATE roles SET permissions = :permissions WHERE id = :id");
        $stmt->execute([
            'permissions' => json_encode($permissions),
            'id' => $role['id']
        ]);
        
        echo "<p style='color: green;'>Audit roluna yoxlamaları görmək icazəsi uğurla əlavə edildi!</p>";
    } else {
        echo "<p style='color: red;'>Audit rolu tapılmadı!</p>";
    }
    
    echo "<p><a href='index.php'>Giriş səhifəsinə qayıt</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Xəta baş verdi: " . $e->getMessage() . "</p>";
}
?>
