// Audit form JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // <PERSON>le add more images button
    const addMoreButtons = document.querySelectorAll('.add-more-images');

    // Note: Add more images functionality is now handled by universal event delegation below

    // Show/hide image upload based on "No" selection
    const statusRadios = document.querySelectorAll('input[type="radio"][name^="status"]');

    statusRadios.forEach(function(radio) {
        radio.addEventListener('change', function() {
            const itemId = this.getAttribute('data-item-id');
            const tr = this.closest('tr');
            const nextTr = tr.nextElementSibling;

            if (this.value === 'no') {
                // Check if next row is an image row
                if (nextTr && nextTr.classList.contains('image-row')) {
                    nextTr.style.display = 'table-row';
                } else {
                    // Create new image row if it doesn't exist
                    const imageRow = document.createElement('tr');
                    imageRow.classList.add('image-row');
                    imageRow.innerHTML = `
                        <td colspan="4">
                            <div id="image-upload-container-${itemId}">
                                <label class="form-label">Şəkillər:</label>
                                <div class="mb-2">
                                    <div class="image-upload-container">
                                        <div class="mb-2">
                                            <div class="btn-group w-100 mb-2" role="group">
                                                <button type="button" class="btn btn-outline-primary camera-btn" data-item-id="${itemId}">
                                                    <i class="fas fa-camera"></i> Kameradan çək
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary gallery-btn" data-item-id="${itemId}">
                                                    <i class="fas fa-images"></i> Qalereyadan seç
                                                </button>
                                            </div>
                                            <input type="file" class="form-control image-upload d-none" name="images[${itemId}][]" accept="image/*" multiple id="gallery-input-${itemId}">
                                            <input type="file" class="form-control image-upload d-none" name="images[${itemId}][]" accept="image/*" capture="environment" multiple id="camera-input-${itemId}">
                                            <div class="image-preview"></div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-success add-more-images" data-item-id="${itemId}">
                                            <i class="fas fa-plus"></i> Daha çox şəkil əlavə et
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </td>
                    `;
                    tr.parentNode.insertBefore(imageRow, tr.nextSibling);

                    // Initialize add more images button - no need for separate handler since we use event delegation
                }
            } else {
                // Hide image row if it exists
                if (nextTr && nextTr.classList.contains('image-row')) {
                    nextTr.style.display = 'none';
                }
            }
        });
    });

    // Initialize existing status radios
    statusRadios.forEach(function(radio) {
        if (radio.checked && radio.value === 'no') {
            const itemId = radio.getAttribute('data-item-id');
            const tr = radio.closest('tr');
            const nextTr = tr.nextElementSibling;

            if (nextTr && nextTr.classList.contains('image-row')) {
                nextTr.style.display = 'table-row';
            }
        } else if (radio.checked && radio.value !== 'no') {
            const tr = radio.closest('tr');
            const nextTr = tr.nextElementSibling;

            if (nextTr && nextTr.classList.contains('image-row')) {
                nextTr.style.display = 'none';
            }
        }
    });

    // Handle score input validation
    const scoreInputs = document.querySelectorAll('input[type="number"][name^="score"]');

    scoreInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            let value = parseInt(this.value);

            if (isNaN(value)) {
                this.value = '';
            } else if (value < 1) {
                this.value = 1;
            } else if (value > 10) {
                this.value = 10;
            }
        });
    });

    // Form validation before submit
    const auditForm = document.getElementById('audit-form');

    if (auditForm) {
        auditForm.addEventListener('submit', function(e) {
            const requiredInputs = document.querySelectorAll('.required-field');
            let hasError = false;

            requiredInputs.forEach(function(input) {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    hasError = true;
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            if (hasError) {
                e.preventDefault();
                alert('Zəhmət olmasa bütün tələb olunan sahələri doldurun.');

                // Scroll to first error
                const firstError = document.querySelector('.is-invalid');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
    }

    // Universal event handler for camera, gallery and add more buttons
    document.addEventListener('click', function(e) {
        // Handle camera button clicks
        if (e.target.closest('.camera-btn')) {
            const button = e.target.closest('.camera-btn');
            const itemId = button.getAttribute('data-item-id');
            const inputId = button.getAttribute('data-input-id');

            // Use specific input ID if available, otherwise use default
            const cameraInputId = inputId || ('camera-input-' + itemId);
            const cameraInput = document.getElementById(cameraInputId);
            if (cameraInput) {
                cameraInput.click();
            }
        }

        // Handle gallery button clicks
        if (e.target.closest('.gallery-btn')) {
            const button = e.target.closest('.gallery-btn');
            const itemId = button.getAttribute('data-item-id');
            const inputId = button.getAttribute('data-input-id');

            // Use specific input ID if available, otherwise use default
            const galleryInputId = inputId || ('gallery-input-' + itemId);
            const galleryInput = document.getElementById(galleryInputId);
            if (galleryInput) {
                galleryInput.click();
            }
        }

        // Handle add more images button clicks
        if (e.target.closest('.add-more-images')) {
            const button = e.target.closest('.add-more-images');
            const itemId = button.getAttribute('data-item-id');
            const container = button.closest('.image-upload-container');
            const inputCount = container.querySelectorAll('.additional-input').length + 1;

            const newInput = document.createElement('div');
            newInput.className = 'mb-2 additional-input';
            newInput.innerHTML = `
                <div class="btn-group w-100 mb-2" role="group">
                    <button type="button" class="btn btn-outline-primary camera-btn" data-item-id="${itemId}" data-input-id="camera-input-${itemId}-${inputCount}">
                        <i class="fas fa-camera"></i> Kameradan çək
                    </button>
                    <button type="button" class="btn btn-outline-secondary gallery-btn" data-item-id="${itemId}" data-input-id="gallery-input-${itemId}-${inputCount}">
                        <i class="fas fa-images"></i> Qalereyadan seç
                    </button>
                </div>
                <input type="file" class="form-control image-upload d-none" name="images[${itemId}][]" accept="image/*" multiple id="gallery-input-${itemId}-${inputCount}">
                <input type="file" class="form-control image-upload d-none" name="images[${itemId}][]" accept="image/*" capture="environment" multiple id="camera-input-${itemId}-${inputCount}">
                <div class="image-preview"></div>
            `;

            container.insertBefore(newInput, button);
        }
    });

    // Handle file selection for both camera and gallery
    document.addEventListener('change', function(e) {
        if (e.target.matches('.image-upload')) {
            const files = e.target.files;

            if (files.length > 0) {
                // Find the closest preview container for this specific input
                const inputContainer = e.target.closest('.mb-2') || e.target.closest('.additional-input');
                let preview = inputContainer ? inputContainer.querySelector('.image-preview') : null;

                if (!preview) {
                    preview = document.createElement('div');
                    preview.className = 'image-preview mt-2';
                    if (inputContainer) {
                        inputContainer.appendChild(preview);
                    }
                }

                preview.innerHTML = '';

                // Show file count and names
                if (files.length === 1) {
                    const fileDiv = document.createElement('div');
                    fileDiv.className = 'selected-file mb-1';
                    fileDiv.innerHTML = `<small class="text-success"><i class="fas fa-check"></i> ${files[0].name}</small>`;
                    preview.appendChild(fileDiv);
                } else {
                    const countDiv = document.createElement('div');
                    countDiv.className = 'selected-file mb-1';
                    countDiv.innerHTML = `<small class="text-success"><i class="fas fa-check"></i> ${files.length} şəkil seçildi</small>`;
                    preview.appendChild(countDiv);

                    // Show individual file names
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        const fileDiv = document.createElement('div');
                        fileDiv.className = 'selected-file mb-1 ms-3';
                        fileDiv.innerHTML = `<small class="text-muted">• ${file.name}</small>`;
                        preview.appendChild(fileDiv);
                    }
                }
            }
        }
    });
});
