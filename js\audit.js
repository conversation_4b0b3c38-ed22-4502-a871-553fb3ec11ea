// Audit form JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // <PERSON>le add more images button
    const addMoreButtons = document.querySelectorAll('.add-more-images');

    addMoreButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const itemId = this.getAttribute('data-item-id');
            const container = this.closest('.image-upload-container');

            const newInput = document.createElement('div');
            newInput.className = 'mb-2';
            newInput.innerHTML = `<input type="file" class="form-control image-upload" name="images[${itemId}][]" accept="image/*">`;

            container.insertBefore(newInput, button);
        });
    });

    // Show/hide image upload based on "No" selection
    const statusRadios = document.querySelectorAll('input[type="radio"][name^="status"]');

    statusRadios.forEach(function(radio) {
        radio.addEventListener('change', function() {
            const itemId = this.getAttribute('data-item-id');
            const tr = this.closest('tr');
            const nextTr = tr.nextElementSibling;

            if (this.value === 'no') {
                // Check if next row is an image row
                if (nextTr && nextTr.classList.contains('image-row')) {
                    nextTr.style.display = 'table-row';
                } else {
                    // Create new image row if it doesn't exist
                    const imageRow = document.createElement('tr');
                    imageRow.classList.add('image-row');
                    imageRow.innerHTML = `
                        <td colspan="4">
                            <div id="image-upload-container-${itemId}">
                                <label class="form-label">Şəkillər:</label>
                                <div class="mb-2">
                                    <div class="image-upload-container">
                                        <div class="mb-2">
                                            <input type="file" class="form-control image-upload" name="images[${itemId}][]" accept="image/*" multiple>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-success add-more-images" data-item-id="${itemId}">
                                            <i class="fas fa-plus"></i> Daha çox şəkil əlavə et
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </td>
                    `;
                    tr.parentNode.insertBefore(imageRow, tr.nextSibling);

                    // Initialize add more images button
                    const addMoreButton = imageRow.querySelector('.add-more-images');
                    addMoreButton.addEventListener('click', function() {
                        const itemId = this.getAttribute('data-item-id');
                        const container = this.closest('.image-upload-container');

                        const newInput = document.createElement('div');
                        newInput.className = 'mb-2';
                        newInput.innerHTML = `<input type="file" class="form-control image-upload" name="images[${itemId}][]" accept="image/*">`;

                        container.insertBefore(newInput, this);
                    });
                }
            } else {
                // Hide image row if it exists
                if (nextTr && nextTr.classList.contains('image-row')) {
                    nextTr.style.display = 'none';
                }
            }
        });
    });

    // Initialize existing status radios
    statusRadios.forEach(function(radio) {
        if (radio.checked && radio.value === 'no') {
            const itemId = radio.getAttribute('data-item-id');
            const tr = radio.closest('tr');
            const nextTr = tr.nextElementSibling;

            if (nextTr && nextTr.classList.contains('image-row')) {
                nextTr.style.display = 'table-row';
            }
        } else if (radio.checked && radio.value !== 'no') {
            const tr = radio.closest('tr');
            const nextTr = tr.nextElementSibling;

            if (nextTr && nextTr.classList.contains('image-row')) {
                nextTr.style.display = 'none';
            }
        }
    });

    // Handle score input validation
    const scoreInputs = document.querySelectorAll('input[type="number"][name^="score"]');

    scoreInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            let value = parseInt(this.value);

            if (isNaN(value)) {
                this.value = '';
            } else if (value < 1) {
                this.value = 1;
            } else if (value > 10) {
                this.value = 10;
            }
        });
    });

    // Form validation before submit
    const auditForm = document.getElementById('audit-form');

    if (auditForm) {
        auditForm.addEventListener('submit', function(e) {
            const requiredInputs = document.querySelectorAll('.required-field');
            let hasError = false;

            requiredInputs.forEach(function(input) {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    hasError = true;
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            if (hasError) {
                e.preventDefault();
                alert('Zəhmət olmasa bütün tələb olunan sahələri doldurun.');

                // Scroll to first error
                const firstError = document.querySelector('.is-invalid');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
    }

    // Camera and Gallery button handlers
    document.addEventListener('click', function(e) {
        if (e.target.closest('.camera-btn')) {
            const itemId = e.target.closest('.camera-btn').getAttribute('data-item-id');
            const cameraInput = document.getElementById('camera-input-' + itemId);
            if (cameraInput) {
                cameraInput.click();
            }
        }

        if (e.target.closest('.gallery-btn')) {
            const itemId = e.target.closest('.gallery-btn').getAttribute('data-item-id');
            const galleryInput = document.getElementById('gallery-input-' + itemId);
            if (galleryInput) {
                galleryInput.click();
            }
        }
    });

    // Handle file selection for both camera and gallery
    document.addEventListener('change', function(e) {
        if (e.target.matches('.image-upload')) {
            const files = e.target.files;
            if (files.length > 0) {
                const container = e.target.closest('.image-upload-container');
                if (container) {
                    let preview = container.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('div');
                        preview.className = 'image-preview mt-2';
                        container.appendChild(preview);
                    }

                    preview.innerHTML = '';
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        const fileDiv = document.createElement('div');
                        fileDiv.className = 'selected-file mb-1';
                        fileDiv.innerHTML = `<small class="text-success"><i class="fas fa-check"></i> ${file.name}</small>`;
                        preview.appendChild(fileDiv);
                    }
                }
            }
        }
    });
});
