# Audit System

Mağazaların yoxlanması üçün audit sistemi.

## Sistem haqqında

Bu sistem mağazaların audit yoxlamalarını aparmaq üçün nəzərdə tutulmuşdur. Sistem aşağıdakı əsas funksionallıqları təqdim edir:

- Mağazaların idarə edilməsi
- Yoxlama şablonlarının yaradılması və redaktəsi
- Yoxlamaların aparılması və qiymətləndirilməsi
- Tamamlanmış yoxlamaların filtrlənməsi və hesabatların çıxarılması
- Ziyarət planının tərtib edilməsi
- İstifadəçi və rolların idarə edilməsi

## Texniki tələblər

- PHP 7.4 və ya daha yüksək
- MySQL 5.7 və ya daha yüksək
- Web server (Apache, Nginx və s.)

## Qur<PERSON><PERSON><PERSON><PERSON><PERSON> qay<PERSON>ı

1. Bütün faylları `checkup.nurcollection.az` qovluğuna köçürün.

2. MySQL verilənlər bazasını yaradın:
   - Verilənlər bazası adı: `brendh5_checkup`
   - İstifadəçi adı: `brendh5_checkup_user`
   - Şifrə: `Zefer*2021`

3. `database.sql` faylını verilənlər bazasına import edin:
   ```
   mysql -u brendh5_checkup_user -p brendh5_checkup < database.sql
   ```
   
   Və ya phpMyAdmin vasitəsilə import edin:
   - phpMyAdmin-a daxil olun
   - `brendh5_checkup` verilənlər bazasını seçin
   - "Import" tabına keçin
   - `database.sql` faylını seçin və "Go" düyməsinə klikləyin

4. `config.php` faylında verilənlər bazası parametrlərini yoxlayın və lazım olarsa düzəliş edin.

5. `uploads` qovluğuna yazma icazələrini verin:
   ```
   chmod 777 uploads
   ```

6. Sistemə daxil olmaq üçün aşağıdakı standart istifadəçi məlumatlarından istifadə edin:
   - Admin: Admin123
   - Audit: Audit123
   - Menecer: Menecer123

## Fayl strukturu

- `index.php` - Giriş səhifəsi
- `dashboard.php` - Ana səhifə
- `stores.php` - Mağazalar səhifəsi
- `audits.php` - Yoxlamalar səhifəsi
- `completed.php` - Tamamlanmış yoxlamalar səhifəsi
- `visit_plan.php` - Ziyarət planı səhifəsi
- `settings.php` - Tənzimləmələr səhifəsi
- `config.php` - Konfiqurasiya faylı
- `functions.php` - Ümumi funksiyalar
- `auth.php` - Autentifikasiya funksiyaları
- `css/` - CSS faylları
- `js/` - JavaScript faylları
- `uploads/` - Yüklənən şəkillər üçün qovluq
- `database.sql` - Verilənlər bazası strukturu

## İstifadəçi rolları

1. **Admin**
   - Bütün icazələrə malikdir

2. **Audit**
   - Mağazaları görmək
   - Yoxlama yaratmaq
   - Yoxlamanı redaktə etmək
   - Tamamlanmış yoxlamaları görmək
   - Ziyarət planında istənilən tarixə mağaza ziyarəti seçə bilmək

3. **Menecer**
   - Mağazaları görmək
   - Yoxlamaları görmək
   - Tamamlanmış yoxlamaları görmək
   - Ziyarət planında istənilən tarixə mağaza ziyarəti seçə bilmək
