<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('create_audit');

// Get stores and templates
$stores = getAllStores();
$templates = getAllTemplates();

// Pre-select store if provided in URL
$selectedStoreId = isset($_GET['store_id']) ? (int)$_GET['store_id'] : 0;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $storeId = (int)$_POST['store_id'];
    $templateId = (int)$_POST['template_id'];
    
    // Validate input
    if ($storeId <= 0 || $templateId <= 0) {
        flash('error_message', 'Zəhmət olmasa mağaza və şablon seçin', 'alert alert-danger');
    } else {
        global $pdo;
        
        try {
            // Start transaction
            $pdo->beginTransaction();
            
            // Create new audit
            $stmt = $pdo->prepare("INSERT INTO audits (store_id, template_id, user_id) VALUES (:store_id, :template_id, :user_id)");
            $stmt->execute([
                'store_id' => $storeId,
                'template_id' => $templateId,
                'user_id' => $_SESSION['user_id']
            ]);
            
            $auditId = $pdo->lastInsertId();
            
            // Get template items
            $template = getTemplateById($templateId);
            
            // Create audit items for each template item
            foreach ($template['sections'] as $section) {
                foreach ($section['items'] as $item) {
                    $stmt = $pdo->prepare("INSERT INTO audit_items (audit_id, template_item_id) VALUES (:audit_id, :template_item_id)");
                    $stmt->execute([
                        'audit_id' => $auditId,
                        'template_item_id' => $item['id']
                    ]);
                }
            }
            
            // Commit transaction
            $pdo->commit();
            
            flash('success_message', 'Yoxlama uğurla yaradıldı');
            redirect('audit_view.php?id=' . $auditId);
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            flash('error_message', 'Xəta baş verdi: ' . $e->getMessage(), 'alert alert-danger');
        }
    }
}

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Yeni yoxlama</h1>
    
    <a href="audits.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Geri
    </a>
</div>

<div class="form-container">
    <form action="audit_form.php" method="post">
        <div class="mb-3">
            <label for="store_id" class="form-label">Mağaza *</label>
            <select class="form-select" id="store_id" name="store_id" required>
                <option value="">Mağaza seçin</option>
                <?php foreach ($stores as $store): ?>
                <option value="<?php echo $store['id']; ?>" <?php echo $selectedStoreId == $store['id'] ? 'selected' : ''; ?>>
                    <?php echo $store['name']; ?>
                </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="template_id" class="form-label">Şablon *</label>
            <select class="form-select" id="template_id" name="template_id" required>
                <option value="">Şablon seçin</option>
                <?php foreach ($templates as $template): ?>
                <option value="<?php echo $template['id']; ?>">
                    <?php echo $template['name']; ?>
                </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <a href="audits.php" class="btn btn-secondary me-md-2">Ləğv et</a>
            <button type="submit" class="btn btn-primary">Yoxlama yarat</button>
        </div>
    </form>
</div>

<?php include 'footer.php'; ?>
