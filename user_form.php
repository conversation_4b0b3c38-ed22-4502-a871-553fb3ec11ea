<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and admin role
requireLogin();

if ($_SESSION['user_role'] !== 'Admin') {
    flash('error_message', 'Bu səhifəyə giriş icazəniz yoxdur', 'alert alert-danger');
    redirect('dashboard.php');
}

// Initialize variables
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$user = [
    'username' => '',
    'role_id' => ''
];
$isEdit = false;

// If editing, get user data
if ($id > 0) {
    $user = getUserById($id);
    
    if (!$user) {
        flash('error_message', 'İstifadəçi tapılmadı', 'alert alert-danger');
        redirect('settings.php');
    }
    
    $isEdit = true;
}

// Get all roles
$roles = getAllRoles();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $roleId = (int)$_POST['role_id'];
    
    // Validate input
    if (empty($username) || (!$isEdit && empty($password)) || $roleId <= 0) {
        flash('error_message', 'Zəhmət olmasa bütün tələb olunan sahələri doldurun', 'alert alert-danger');
    } else {
        if ($isEdit) {
            // Update user
            if (updateUser($id, $username, $password, $roleId)) {
                flash('success_message', 'İstifadəçi uğurla yeniləndi');
                redirect('settings.php');
            } else {
                flash('error_message', 'İstifadəçi yenilənərkən xəta baş verdi', 'alert alert-danger');
            }
        } else {
            // Create new user
            if (register($username, $password, $roleId)) {
                flash('success_message', 'İstifadəçi uğurla yaradıldı');
                redirect('settings.php');
            } else {
                flash('error_message', 'İstifadəçi yaradılarkən xəta baş verdi. İstifadəçi adı artıq mövcud ola bilər.', 'alert alert-danger');
            }
        }
    }
}

// Include header
include 'header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><?php echo $isEdit ? 'İstifadəçini redaktə et' : 'Yeni istifadəçi'; ?></h1>
    
    <a href="settings.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Geri
    </a>
</div>

<div class="form-container">
    <form action="user_form.php<?php echo $isEdit ? '?id=' . $id : ''; ?>" method="post">
        <div class="mb-3">
            <label for="username" class="form-label">İstifadəçi adı *</label>
            <input type="text" class="form-control" id="username" name="username" value="<?php echo $user['username']; ?>" required>
        </div>
        
        <div class="mb-3">
            <label for="password" class="form-label"><?php echo $isEdit ? 'Şifrə (dəyişdirmək üçün doldurun)' : 'Şifrə *'; ?></label>
            <input type="password" class="form-control" id="password" name="password" <?php echo $isEdit ? '' : 'required'; ?>>
        </div>
        
        <div class="mb-3">
            <label for="role_id" class="form-label">Rol *</label>
            <select class="form-select" id="role_id" name="role_id" required>
                <option value="">Rol seçin</option>
                <?php foreach ($roles as $role): ?>
                <option value="<?php echo $role['id']; ?>" <?php echo $user['role_id'] == $role['id'] ? 'selected' : ''; ?>>
                    <?php echo $role['name']; ?>
                </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <a href="settings.php" class="btn btn-secondary me-md-2">Ləğv et</a>
            <button type="submit" class="btn btn-primary">Yadda saxla</button>
        </div>
    </form>
</div>

<?php include 'footer.php'; ?>
