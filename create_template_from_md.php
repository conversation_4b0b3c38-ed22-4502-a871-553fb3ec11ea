<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'auth.php';

// Require login and permission
requireLogin();
requirePermission('create_audit');

// Read the suallar.md file
$mdContent = file_get_contents('suallar.md');
$lines = explode("\n", $mdContent);

// Parse the content
$sections = [];
$currentSection = null;
$currentItems = [];

foreach ($lines as $line) {
    $line = trim($line);
    
    // Skip empty lines
    if (empty($line)) {
        continue;
    }
    
    // Check if this is a section header (bold text with **)
    if (preg_match('/^\*\*(.*)\*\*$/', $line, $matches)) {
        // If we have a current section, add it to the sections array
        if ($currentSection !== null && !empty($currentItems)) {
            $sections[] = [
                'name' => $currentSection,
                'items' => $currentItems
            ];
        }
        
        // Start a new section
        $currentSection = trim($matches[1]);
        $currentItems = [];
    } else {
        // This is an item for the current section
        if ($currentSection !== null) {
            $currentItems[] = $line;
        }
    }
}

// Add the last section
if ($currentSection !== null && !empty($currentItems)) {
    $sections[] = [
        'name' => $currentSection,
        'items' => $currentItems
    ];
}

// Create the template in the database
if (!empty($sections)) {
    global $pdo;
    
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Create template
        $templateName = "Mağaza Yoxlama Şablonu - " . date('d.m.Y');
        $stmt = $pdo->prepare("INSERT INTO templates (name) VALUES (:name)");
        $stmt->execute(['name' => $templateName]);
        
        $templateId = $pdo->lastInsertId();
        
        // Add sections and items
        foreach ($sections as $sectionIndex => $section) {
            // Insert section
            $stmt = $pdo->prepare("INSERT INTO template_sections (template_id, name, order_num) VALUES (:template_id, :name, :order_num)");
            $stmt->execute([
                'template_id' => $templateId,
                'name' => $section['name'],
                'order_num' => $sectionIndex + 1
            ]);
            
            $sectionId = $pdo->lastInsertId();
            
            // Insert items
            foreach ($section['items'] as $itemIndex => $itemName) {
                $stmt = $pdo->prepare("INSERT INTO template_items (section_id, name, order_num) VALUES (:section_id, :name, :order_num)");
                $stmt->execute([
                    'section_id' => $sectionId,
                    'name' => $itemName,
                    'order_num' => $itemIndex + 1
                ]);
            }
        }
        
        // Commit transaction
        $pdo->commit();
        
        echo "Şablon uğurla yaradıldı! Şablon ID: " . $templateId;
        echo "<br><a href='templates.php'>Şablonlar səhifəsinə qayıt</a>";
    } catch (PDOException $e) {
        // Rollback transaction on error
        $pdo->rollBack();
        echo "Xəta baş verdi: " . $e->getMessage();
    }
} else {
    echo "Heç bir bölmə tapılmadı.";
}
?>
